#!/usr/bin/env node
/**
 * Bitcoin Forensic Investigation Tool v3.0
 *
 * A comprehensive cryptocurrency forensics tool designed specifically for Bitcoin scam victims.
 * This tool provides automated transaction tracing, AI-powered analysis, and professional
 * evidence collection capabilities in a user-friendly CLI interface.
 *
 * Features:
 * - Automated Bitcoin transaction tracing
 * - AI-powered suspicious activity detection
 * - Professional evidence collection and chain of custody
 * - Comprehensive reporting for legal proceedings
 * - User-friendly interface for non-technical users
 *
 * <AUTHOR> Forensics Team
 * @version 3.0.0
 * @license MIT
 */
import { CLIInterface } from './cli/interface';
export { CLIInterface };
export { BitcoinForensicsInvestigator } from './core/investigator';
export { AnalysisService } from './services/analysis';
export { BitcoinAPIService } from './services/api';
export * from './types';
export * from './utils/validation';
export * from './utils/logger';
//# sourceMappingURL=index.d.ts.map