/**
 * Shared validation utilities
 * Consolidates validation logic that was duplicated across multiple files
 */
import { ForensicsError } from './error-handler';
/**
 * Validates Bitcoin address format
 */
export declare function validateBitcoinAddress(address: string): boolean;
/**
 * Determines Bitcoin address type
 */
export declare function getBitcoinAddressType(address: string): 'legacy' | 'segwit' | 'taproot' | 'unknown';
/**
 * Validates transaction ID format
 */
export declare function validateTransactionId(txid: string): boolean;
/**
 * Validates investigation depth
 */
export declare function validateDepth(depth: number): boolean;
/**
 * Validates Bitcoin amount
 */
export declare function validateBtcAmount(amount: number): boolean;
/**
 * Validates email format
 */
export declare function validateEmail(email: string): boolean;
/**
 * Validates date format (YYYY-MM-DD)
 */
export declare function validateDate(dateString: string): boolean;
/**
 * Validates numeric input with range constraints
 */
export declare function validateNumericInput(value: number, fieldName: string, min?: number, max?: number): void;
/**
 * Validates string input with length constraints
 */
export declare function validateStringInput(value: string, fieldName: string, minLength?: number, maxLength?: number, pattern?: RegExp): void;
/**
 * Sanitizes input string to prevent XSS and injection attacks
 */
export declare function sanitizeInput(input: string): string;
/**
 * Validates file path to prevent directory traversal
 */
export declare function validateFilePath(filePath: string): boolean;
/**
 * Validates URL format and ensures HTTPS
 */
export declare function validateSecureUrl(url: string): boolean;
/**
 * Formats validation errors for display
 */
export declare function formatValidationErrors(errors: string[]): string;
/**
 * Creates a validation error with consistent formatting
 */
export declare function createValidationError(message: string, details?: any): ForensicsError;
//# sourceMappingURL=shared-validation.d.ts.map