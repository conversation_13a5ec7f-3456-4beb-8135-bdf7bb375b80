import winston from 'winston';
export declare const logger: winston.Logger;
export declare const logInvestigationStart: (investigationId: string, params: any) => void;
export declare const logInvestigationEnd: (investigationId: string, results: any) => void;
export declare const logAPICall: (endpoint: string, success: boolean, duration: number) => void;
export declare const logError: (error: Error, context?: any) => void;
export declare const logUserAction: (action: string, details?: any) => void;
export declare const logEvidenceCreated: (evidenceId: string, type: string, description: string) => void;
export declare const logAuditEvent: (investigationId: string, event: string, details: any) => void;
export declare const setLogLevel: (level: string) => void;
export default logger;
//# sourceMappingURL=logger.d.ts.map