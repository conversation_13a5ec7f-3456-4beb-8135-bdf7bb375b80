"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setLogLevel = exports.logAuditEvent = exports.logEvidenceCreated = exports.logUserAction = exports.logError = exports.logAPICall = exports.logInvestigationEnd = exports.logInvestigationStart = exports.logger = void 0;
const winston_1 = __importDefault(require("winston"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
// Ensure logs directory exists
const logsDir = path_1.default.join(process.cwd(), 'logs');
if (!fs_1.default.existsSync(logsDir)) {
    fs_1.default.mkdirSync(logsDir, { recursive: true });
}
// Custom format for console output
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.timestamp({ format: 'HH:mm:ss' }), winston_1.default.format.printf(({ timestamp, level, message, ...meta }) => {
    const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level}]: ${message} ${metaStr}`;
}));
// Custom format for file output
const fileFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json());
// Create logger instance
exports.logger = winston_1.default.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: fileFormat,
    defaultMeta: { service: 'bitcoin-forensics' },
    transports: [
        // File transport for all logs
        new winston_1.default.transports.File({
            filename: path_1.default.join(logsDir, 'investigation.log'),
            maxsize: 10 * 1024 * 1024, // 10MB
            maxFiles: 5,
        }),
        // File transport for errors only
        new winston_1.default.transports.File({
            filename: path_1.default.join(logsDir, 'error.log'),
            level: 'error',
            maxsize: 5 * 1024 * 1024, // 5MB
            maxFiles: 3,
        }),
    ],
});
// Add console transport in development or when explicitly requested
if (process.env.NODE_ENV !== 'production' || process.env.CONSOLE_LOGS === 'true') {
    exports.logger.add(new winston_1.default.transports.Console({
        format: consoleFormat,
    }));
}
// Helper functions for structured logging
const logInvestigationStart = (investigationId, params) => {
    exports.logger.info('Investigation started', {
        investigationId,
        action: 'investigation_start',
        parameters: params,
    });
};
exports.logInvestigationStart = logInvestigationStart;
const logInvestigationEnd = (investigationId, results) => {
    exports.logger.info('Investigation completed', {
        investigationId,
        action: 'investigation_complete',
        results: {
            transactionCount: results.transactionCount,
            addressCount: results.addressCount,
            totalAmount: results.totalAmount,
        },
    });
};
exports.logInvestigationEnd = logInvestigationEnd;
const logAPICall = (endpoint, success, duration) => {
    exports.logger.debug('API call completed', {
        action: 'api_call',
        endpoint,
        success,
        duration,
    });
};
exports.logAPICall = logAPICall;
const logError = (error, context) => {
    exports.logger.error('Error occurred', {
        action: 'error',
        error: {
            message: error.message,
            stack: error.stack,
            name: error.name,
        },
        context,
    });
};
exports.logError = logError;
const logUserAction = (action, details) => {
    exports.logger.info('User action', {
        action: 'user_action',
        userAction: action,
        details,
    });
};
exports.logUserAction = logUserAction;
const logEvidenceCreated = (evidenceId, type, description) => {
    exports.logger.info('Evidence created', {
        action: 'evidence_created',
        evidenceId,
        evidenceType: type,
        description,
    });
};
exports.logEvidenceCreated = logEvidenceCreated;
const logAuditEvent = (investigationId, event, details) => {
    exports.logger.info('Audit event', {
        action: 'audit_event',
        investigationId,
        event,
        details,
    });
};
exports.logAuditEvent = logAuditEvent;
// Set log level dynamically
const setLogLevel = (level) => {
    exports.logger.level = level;
    exports.logger.info(`Log level set to ${level}`);
};
exports.setLogLevel = setLogLevel;
exports.default = exports.logger;
//# sourceMappingURL=logger.js.map