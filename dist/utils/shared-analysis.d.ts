/**
 * Shared analysis utilities
 * Consolidates analysis logic that was duplicated across multiple services
 */
import { TransactionInfo, AddressInfo, SuspiciousActivityDetail } from '../types';
type RiskLevel = 'MINIMAL' | 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
/**
 * Calculates risk level based on numeric score
 */
export declare function calculateRiskLevel(score: number): RiskLevel;
/**
 * Calculates composite risk score from multiple factors
 */
export declare function calculateCompositeRiskScore(baseScore: number, suspiciousActivities: string[], additionalFactors?: Record<string, number>): number;
/**
 * Analyzes transaction timing patterns
 */
export declare function analyzeTransactionTiming(transactions: TransactionInfo[]): {
    averageIntervalSeconds: number;
    rapidSuccessionCount: number;
    totalIntervals: number;
    suspiciousTiming: boolean;
    timePatterns: string[];
};
/**
 * Analyzes transaction amount patterns
 */
export declare function analyzeTransactionAmounts(transactions: TransactionInfo[]): {
    totalAmount: number;
    averageAmount: number;
    maxAmount: number;
    minAmount: number;
    roundAmountsCount: number;
    similarAmountsCount: number;
    potentialStructuring: boolean;
    potentialSplitting: boolean;
};
/**
 * Analyzes address reuse patterns
 */
export declare function analyzeAddressReuse(transactions: TransactionInfo[]): {
    uniqueFromAddresses: number;
    uniqueToAddresses: number;
    reusedFromAddresses: Record<string, number>;
    reusedToAddresses: Record<string, number>;
    addressReuseDetected: boolean;
};
/**
 * Generates risk indicators summary
 */
export declare function generateRiskIndicators(transactions: TransactionInfo[], addressAnalysis: AddressInfo[], maxDepth: number): {
    riskScore: number;
    riskLevel: RiskLevel;
    riskFactors: string[];
    totalAmount: number;
    transactionCount: number;
    maxDepth: number;
};
/**
 * Formats suspicious activity results for consistency
 */
export declare function formatSuspiciousActivityResult(detected: boolean, severity: number, confidence: number, details?: any): SuspiciousActivityDetail;
export {};
//# sourceMappingURL=shared-analysis.d.ts.map