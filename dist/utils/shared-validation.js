"use strict";
/**
 * Shared validation utilities
 * Consolidates validation logic that was duplicated across multiple files
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateBitcoinAddress = validateBitcoinAddress;
exports.getBitcoinAddressType = getBitcoinAddressType;
exports.validateTransactionId = validateTransactionId;
exports.validateDepth = validateDepth;
exports.validateBtcAmount = validateBtcAmount;
exports.validateEmail = validateEmail;
exports.validateDate = validateDate;
exports.validateNumericInput = validateNumericInput;
exports.validateStringInput = validateStringInput;
exports.sanitizeInput = sanitizeInput;
exports.validateFilePath = validateFilePath;
exports.validateSecureUrl = validateSecureUrl;
exports.formatValidationErrors = formatValidationErrors;
exports.createValidationError = createValidationError;
const constants_1 = require("./constants");
const error_handler_1 = require("./error-handler");
/**
 * Validates Bitcoin address format
 */
function validateBitcoinAddress(address) {
    if (!address || typeof address !== 'string') {
        return false;
    }
    // Check length constraints
    if (address.length < 26 || address.length > 62) {
        return false;
    }
    // Check against known patterns
    return Object.values(constants_1.BITCOIN_ADDRESS_PATTERNS).some(pattern => pattern.test(address));
}
/**
 * Determines Bitcoin address type
 */
function getBitcoinAddressType(address) {
    if (!validateBitcoinAddress(address)) {
        return 'unknown';
    }
    // Check Taproot first (bc1p...) since it's more specific
    if (constants_1.BITCOIN_ADDRESS_PATTERNS.TAPROOT.test(address)) {
        return 'taproot';
    }
    if (constants_1.BITCOIN_ADDRESS_PATTERNS.LEGACY.test(address)) {
        return 'legacy';
    }
    if (constants_1.BITCOIN_ADDRESS_PATTERNS.SEGWIT.test(address)) {
        return 'segwit';
    }
    return 'unknown';
}
/**
 * Validates transaction ID format
 */
function validateTransactionId(txid) {
    if (!txid || typeof txid !== 'string') {
        return false;
    }
    return constants_1.TRANSACTION_ID_PATTERN.test(txid);
}
/**
 * Validates investigation depth
 */
function validateDepth(depth) {
    return Number.isInteger(depth) && depth >= 1 && depth <= 10;
}
/**
 * Validates Bitcoin amount
 */
function validateBtcAmount(amount) {
    return typeof amount === 'number' && amount > 0 && amount <= 21000000;
}
/**
 * Validates email format
 */
function validateEmail(email) {
    if (!email || typeof email !== 'string') {
        return false;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
/**
 * Validates date format (YYYY-MM-DD)
 */
function validateDate(dateString) {
    if (!dateString || typeof dateString !== 'string') {
        return false;
    }
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(dateString)) {
        return false;
    }
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime());
}
/**
 * Validates numeric input with range constraints
 */
function validateNumericInput(value, fieldName, min, max) {
    if (typeof value !== 'number' || isNaN(value)) {
        throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_INPUT_PARAMETERS, `${fieldName} must be a valid number`);
    }
    if (min !== undefined && value < min) {
        throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_INPUT_PARAMETERS, `${fieldName} must be at least ${min}`);
    }
    if (max !== undefined && value > max) {
        throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_INPUT_PARAMETERS, `${fieldName} must be at most ${max}`);
    }
}
/**
 * Validates string input with length constraints
 */
function validateStringInput(value, fieldName, minLength, maxLength, pattern) {
    if (!value || typeof value !== 'string') {
        throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_INPUT_PARAMETERS, `${fieldName} is required and must be a string`);
    }
    if (minLength !== undefined && value.length < minLength) {
        throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_INPUT_PARAMETERS, `${fieldName} must be at least ${minLength} characters long`);
    }
    if (maxLength !== undefined && value.length > maxLength) {
        throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_INPUT_PARAMETERS, `${fieldName} must be at most ${maxLength} characters long`);
    }
    if (pattern && !pattern.test(value)) {
        throw new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_INPUT_PARAMETERS, `${fieldName} format is invalid`);
    }
}
/**
 * Sanitizes input string to prevent XSS and injection attacks
 */
function sanitizeInput(input) {
    if (!input || typeof input !== 'string') {
        return '';
    }
    return input
        .trim()
        .replace(/[<>]/g, '') // Remove potential HTML tags
        .replace(/['"]/g, '') // Remove quotes
        .replace(/javascript:/gi, '') // Remove javascript: protocol
        .replace(/on\w+=/gi, '') // Remove event handlers
        .replace(/script/gi, '') // Remove script tags
        .substring(0, 1000); // Limit length
}
/**
 * Validates file path to prevent directory traversal
 */
function validateFilePath(filePath) {
    if (!filePath || typeof filePath !== 'string') {
        return false;
    }
    // Check for directory traversal patterns
    const dangerousPatterns = [
        '../',
        '..\\',
        './',
        '.\\',
        '/etc/',
        '/proc/',
        '/sys/',
        'C:\\',
        'c:\\',
    ];
    return !dangerousPatterns.some(pattern => filePath.toLowerCase().includes(pattern.toLowerCase()));
}
/**
 * Validates URL format and ensures HTTPS
 */
function validateSecureUrl(url) {
    if (!url || typeof url !== 'string') {
        return false;
    }
    try {
        const urlObj = new URL(url);
        return urlObj.protocol === 'https:';
    }
    catch {
        return false;
    }
}
/**
 * Formats validation errors for display
 */
function formatValidationErrors(errors) {
    if (errors.length === 0) {
        return '';
    }
    if (errors.length === 1) {
        return `❌ ${errors[0]}`;
    }
    return `❌ Multiple validation errors:\n${errors.map(error => `   • ${error}`).join('\n')}`;
}
/**
 * Creates a validation error with consistent formatting
 */
function createValidationError(message, details) {
    return new error_handler_1.ForensicsError(error_handler_1.ErrorCode.INVALID_INPUT_PARAMETERS, message, details);
}
//# sourceMappingURL=shared-validation.js.map