{"version": 3, "file": "shared-analysis.js", "sourceRoot": "", "sources": ["../../src/utils/shared-analysis.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAUH,gDAMC;AAKD,kEAsBC;AAKD,4DAwDC;AAKD,8DAiEC;AAKD,kDAiCC;AAKD,wDAqDC;AAKD,wEAYC;AA7RD,2CAA2E;AAK3E;;GAEG;AACH,SAAgB,kBAAkB,CAAC,KAAa;IAC9C,IAAI,KAAK,IAAI,2BAAe,CAAC,QAAQ;QAAE,OAAO,UAAU,CAAC;IACzD,IAAI,KAAK,IAAI,2BAAe,CAAC,IAAI;QAAE,OAAO,MAAM,CAAC;IACjD,IAAI,KAAK,IAAI,2BAAe,CAAC,MAAM;QAAE,OAAO,QAAQ,CAAC;IACrD,IAAI,KAAK,IAAI,2BAAe,CAAC,GAAG;QAAE,OAAO,KAAK,CAAC;IAC/C,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;GAEG;AACH,SAAgB,2BAA2B,CACzC,SAAiB,EACjB,oBAA8B,EAC9B,oBAA4C,EAAE;IAE9C,IAAI,cAAc,GAAG,SAAS,CAAC;IAE/B,kCAAkC;IAClC,KAAK,MAAM,QAAQ,IAAI,oBAAoB,EAAE,CAAC;QAC5C,MAAM,MAAM,GACV,uCAA2B,CAAC,QAAoD,CAAC,CAAC;QACpF,IAAI,MAAM,EAAE,CAAC;YACX,cAAc,IAAI,MAAM,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,yBAAyB;IACzB,KAAK,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC;QACjE,cAAc,IAAI,MAAM,CAAC;IAC3B,CAAC;IAED,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;AACrC,CAAC;AAED;;GAEG;AACH,SAAgB,wBAAwB,CAAC,YAA+B;IAOtE,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC5B,OAAO;YACL,sBAAsB,EAAE,CAAC;YACzB,oBAAoB,EAAE,CAAC;YACvB,cAAc,EAAE,CAAC;YACjB,gBAAgB,EAAE,KAAK;YACvB,YAAY,EAAE,EAAE;SACjB,CAAC;IACJ,CAAC;IAED,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CACjC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAC5E,CAAC;IAEF,MAAM,SAAS,GAAa,EAAE,CAAC;IAC/B,IAAI,oBAAoB,GAAG,CAAC,CAAC;IAC7B,MAAM,YAAY,GAAa,EAAE,CAAC;IAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC1C,MAAM,QAAQ,GACZ,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;QAC9F,SAAS,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,qBAAqB;QAEtD,4CAA4C;QAC5C,IAAI,QAAQ,GAAG,KAAK,EAAE,CAAC;YACrB,oBAAoB,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;IAED,MAAM,eAAe,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE,CAAC,GAAG,GAAG,QAAQ,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;IAElG,kBAAkB;IAClB,IAAI,oBAAoB,GAAG,CAAC,EAAE,CAAC;QAC7B,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACxC,CAAC;IAED,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC;QAC9C,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACvC,CAAC;IAED,MAAM,gBAAgB,GAAG,oBAAoB,GAAG,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;IAE9F,OAAO;QACL,sBAAsB,EAAE,eAAe;QACvC,oBAAoB;QACpB,cAAc,EAAE,SAAS,CAAC,MAAM;QAChC,gBAAgB;QAChB,YAAY;KACb,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,yBAAyB,CAAC,YAA+B;IAUvE,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC9B,OAAO;YACL,WAAW,EAAE,CAAC;YACd,aAAa,EAAE,CAAC;YAChB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,iBAAiB,EAAE,CAAC;YACpB,mBAAmB,EAAE,CAAC;YACtB,oBAAoB,EAAE,KAAK;YAC3B,kBAAkB,EAAE,KAAK;SAC1B,CAAC;IACJ,CAAC;IAED,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;IACrD,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC;IACrE,MAAM,aAAa,GAAG,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC;IACnD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC;IACvC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC;IAEvC,gDAAgD;IAChD,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CACtC,MAAM,CAAC,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAC3E,CAAC,MAAM,CAAC;IAET,kDAAkD;IAClD,IAAI,mBAAmB,GAAG,CAAC,CAAC;IAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACxC,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,SAAS,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAChD,IAAI,IAAI,GAAG,SAAS,GAAG,IAAI,EAAE,CAAC;gBAC5B,YAAY;gBACZ,mBAAmB,EAAE,CAAC;YACxB,CAAC;QACH,CAAC;IACH,CAAC;IAED,4EAA4E;IAC5E,MAAM,oBAAoB,GAAG,GAAG,CAAC,CAAC,UAAU;IAC5C,MAAM,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,GAAG,oBAAoB,CAAC,CAAC,MAAM,CAAC;IACzF,MAAM,oBAAoB,GAAG,iBAAiB,GAAG,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,oBAAoB;IAE3F,6EAA6E;IAC7E,MAAM,kBAAkB,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,GAAG,aAAa,GAAG,CAAC,CAAC;IAE/E,OAAO;QACL,WAAW;QACX,aAAa;QACb,SAAS;QACT,SAAS;QACT,iBAAiB;QACjB,mBAAmB;QACnB,oBAAoB;QACpB,kBAAkB;KACnB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CAAC,YAA+B;IAOjE,MAAM,aAAa,GAA2B,EAAE,CAAC;IACjD,MAAM,WAAW,GAA2B,EAAE,CAAC;IAE/C,KAAK,MAAM,EAAE,IAAI,YAAY,EAAE,CAAC;QAC9B,aAAa,CAAC,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACzE,WAAW,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACnE,CAAC;IAED,MAAM,mBAAmB,GAAG,MAAM,CAAC,WAAW,CAC5C,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAChE,CAAC;IAEF,MAAM,iBAAiB,GAAG,MAAM,CAAC,WAAW,CAC1C,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAC9D,CAAC;IAEF,MAAM,oBAAoB,GACxB,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IAE3F,OAAO;QACL,mBAAmB,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM;QACtD,iBAAiB,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM;QAClD,mBAAmB;QACnB,iBAAiB;QACjB,oBAAoB;KACrB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,sBAAsB,CACpC,YAA+B,EAC/B,eAA8B,EAC9B,QAAgB;IAShB,MAAM,WAAW,GAAa,EAAE,CAAC;IACjC,IAAI,SAAS,GAAG,CAAC,CAAC;IAElB,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;IAC5E,MAAM,gBAAgB,GAAG,YAAY,CAAC,MAAM,CAAC;IAE7C,mBAAmB;IACnB,IAAI,WAAW,GAAG,EAAE,EAAE,CAAC;QACrB,WAAW,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC5C,SAAS,IAAI,CAAC,CAAC;IACjB,CAAC;IAED,8BAA8B;IAC9B,IAAI,gBAAgB,GAAG,EAAE,EAAE,CAAC;QAC1B,WAAW,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC3C,SAAS,IAAI,CAAC,CAAC;IACjB,CAAC;IAED,0BAA0B;IAC1B,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;QACjB,WAAW,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAChD,SAAS,IAAI,CAAC,CAAC;IACjB,CAAC;IAED,sBAAsB;IACtB,MAAM,iBAAiB,GAAG,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;IAC/F,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACjC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACxC,SAAS,IAAI,CAAC,CAAC;IACjB,CAAC;IAED,MAAM,SAAS,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;IAEhD,OAAO;QACL,SAAS;QACT,SAAS;QACT,WAAW;QACX,WAAW;QACX,gBAAgB;QAChB,QAAQ;KACT,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,8BAA8B,CAC5C,QAAiB,EACjB,QAAgB,EAChB,UAAkB,EAClB,UAAe,EAAE;IAEjB,OAAO;QACL,QAAQ;QACR,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,oBAAoB;QAClE,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,oBAAoB;QACtE,OAAO;KACR,CAAC;AACJ,CAAC"}