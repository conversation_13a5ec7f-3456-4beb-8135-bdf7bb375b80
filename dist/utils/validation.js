"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sanitizeInput = exports.formatValidationErrors = exports.validateDate = exports.validateEmail = exports.validateBtcAmount = exports.validateDepth = exports.validateTransactionId = exports.getBitcoinAddressType = exports.validateBitcoinAddress = void 0;
exports.validateConfig = validateConfig;
exports.validateUserInput = validateUserInput;
// Re-export shared validation utilities for backward compatibility
var shared_validation_1 = require("./shared-validation");
Object.defineProperty(exports, "validateBitcoinAddress", { enumerable: true, get: function () { return shared_validation_1.validateBitcoinAddress; } });
Object.defineProperty(exports, "getBitcoinAddressType", { enumerable: true, get: function () { return shared_validation_1.getBitcoinAddressType; } });
Object.defineProperty(exports, "validateTransactionId", { enumerable: true, get: function () { return shared_validation_1.validateTransactionId; } });
Object.defineProperty(exports, "validateDepth", { enumerable: true, get: function () { return shared_validation_1.validateDepth; } });
Object.defineProperty(exports, "validateBtcAmount", { enumerable: true, get: function () { return shared_validation_1.validateBtcAmount; } });
Object.defineProperty(exports, "validateEmail", { enumerable: true, get: function () { return shared_validation_1.validateEmail; } });
Object.defineProperty(exports, "validateDate", { enumerable: true, get: function () { return shared_validation_1.validateDate; } });
Object.defineProperty(exports, "formatValidationErrors", { enumerable: true, get: function () { return shared_validation_1.formatValidationErrors; } });
Object.defineProperty(exports, "sanitizeInput", { enumerable: true, get: function () { return shared_validation_1.sanitizeInput; } });
// Import functions for use in local functions
const shared_validation_2 = require("./shared-validation");
/**
 * Validates investigation configuration
 */
function validateConfig(config) {
    const errors = [];
    if (!config.apiBaseUrl || typeof config.apiBaseUrl !== 'string') {
        errors.push('Invalid API base URL');
    }
    if (!Number.isInteger(config.maxRetries) || config.maxRetries < 1 || config.maxRetries > 10) {
        errors.push('Max retries must be between 1 and 10');
    }
    if (!Number.isInteger(config.requestTimeout) || config.requestTimeout < 1000) {
        errors.push('Request timeout must be at least 1000ms');
    }
    if (!(0, shared_validation_2.validateDepth)(config.maxDepth)) {
        errors.push('Max depth must be between 1 and 10');
    }
    if (typeof config.rateLimitDelay !== 'number' || config.rateLimitDelay < 0) {
        errors.push('Rate limit delay must be a non-negative number');
    }
    return {
        valid: errors.length === 0,
        errors,
    };
}
/**
 * Validates user input for investigation
 */
function validateUserInput(input) {
    const errors = [];
    if (!input.victimName ||
        typeof input.victimName !== 'string' ||
        input.victimName.trim().length === 0) {
        errors.push('Victim name is required');
    }
    if (!input.initialTxid || !(0, shared_validation_2.validateTransactionId)(input.initialTxid)) {
        errors.push('Valid transaction ID is required');
    }
    if (!input.targetAddress || !(0, shared_validation_2.validateBitcoinAddress)(input.targetAddress)) {
        errors.push('Valid Bitcoin address is required');
    }
    if (!(0, shared_validation_2.validateBtcAmount)(input.scamAmount)) {
        errors.push('Valid BTC amount is required');
    }
    if (!(0, shared_validation_2.validateDepth)(input.maxDepth)) {
        errors.push('Investigation depth must be between 1 and 10');
    }
    if (input.contactInfo && !(0, shared_validation_2.validateEmail)(input.contactInfo)) {
        errors.push('Valid email address required for contact info');
    }
    if (input.incidentDate && !(0, shared_validation_2.validateDate)(input.incidentDate)) {
        errors.push('Valid date required for incident date (YYYY-MM-DD format)');
    }
    return {
        valid: errors.length === 0,
        errors,
    };
}
//# sourceMappingURL=validation.js.map