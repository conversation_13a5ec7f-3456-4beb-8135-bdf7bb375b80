"use strict";
/**
 * Shared analysis utilities
 * Consolidates analysis logic that was duplicated across multiple services
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateRiskLevel = calculateRiskLevel;
exports.calculateCompositeRiskScore = calculateCompositeRiskScore;
exports.analyzeTransactionTiming = analyzeTransactionTiming;
exports.analyzeTransactionAmounts = analyzeTransactionAmounts;
exports.analyzeAddressReuse = analyzeAddressReuse;
exports.generateRiskIndicators = generateRiskIndicators;
exports.formatSuspiciousActivityResult = formatSuspiciousActivityResult;
const constants_1 = require("./constants");
/**
 * Calculates risk level based on numeric score
 */
function calculateRiskLevel(score) {
    if (score >= constants_1.RISK_THRESHOLDS.CRITICAL)
        return 'CRITICAL';
    if (score >= constants_1.RISK_THRESHOLDS.HIGH)
        return 'HIGH';
    if (score >= constants_1.RISK_THRESHOLDS.MEDIUM)
        return 'MEDIUM';
    if (score >= constants_1.RISK_THRESHOLDS.LOW)
        return 'LOW';
    return 'MINIMAL';
}
/**
 * Calculates composite risk score from multiple factors
 */
function calculateCompositeRiskScore(baseScore, suspiciousActivities, additionalFactors = {}) {
    let compositeScore = baseScore;
    // Add suspicious activity weights
    for (const activity of suspiciousActivities) {
        const weight = constants_1.SUSPICIOUS_ACTIVITY_WEIGHTS[activity];
        if (weight) {
            compositeScore += weight;
        }
    }
    // Add additional factors
    for (const [factor, weight] of Object.entries(additionalFactors)) {
        compositeScore += weight;
    }
    return Math.max(0, compositeScore);
}
/**
 * Analyzes transaction timing patterns
 */
function analyzeTransactionTiming(transactions) {
    if (transactions.length < 2) {
        return {
            averageIntervalSeconds: 0,
            rapidSuccessionCount: 0,
            totalIntervals: 0,
            suspiciousTiming: false,
            timePatterns: [],
        };
    }
    const sortedTxs = transactions.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    const intervals = [];
    let rapidSuccessionCount = 0;
    const timePatterns = [];
    for (let i = 1; i < sortedTxs.length; i++) {
        const interval = new Date(sortedTxs[i].timestamp).getTime() - new Date(sortedTxs[i - 1].timestamp).getTime();
        intervals.push(interval / 1000); // Convert to seconds
        // Check for rapid succession (< 60 seconds)
        if (interval < 60000) {
            rapidSuccessionCount++;
        }
    }
    const averageInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    // Detect patterns
    if (rapidSuccessionCount > 0) {
        timePatterns.push('rapid_succession');
    }
    if (intervals.some(interval => interval < 10)) {
        timePatterns.push('extremely_rapid');
    }
    const suspiciousTiming = rapidSuccessionCount > 2 || timePatterns.includes('extremely_rapid');
    return {
        averageIntervalSeconds: averageInterval,
        rapidSuccessionCount,
        totalIntervals: intervals.length,
        suspiciousTiming,
        timePatterns,
    };
}
/**
 * Analyzes transaction amount patterns
 */
function analyzeTransactionAmounts(transactions) {
    if (transactions.length === 0) {
        return {
            totalAmount: 0,
            averageAmount: 0,
            maxAmount: 0,
            minAmount: 0,
            roundAmountsCount: 0,
            similarAmountsCount: 0,
            potentialStructuring: false,
            potentialSplitting: false,
        };
    }
    const amounts = transactions.map(tx => tx.amountBtc);
    const totalAmount = amounts.reduce((sum, amount) => sum + amount, 0);
    const averageAmount = totalAmount / amounts.length;
    const maxAmount = Math.max(...amounts);
    const minAmount = Math.min(...amounts);
    // Count round amounts (ending in .0, .00, etc.)
    const roundAmountsCount = amounts.filter(amount => amount === Math.floor(amount) || amount.toString().endsWith('0')).length;
    // Count similar amounts (within 1% of each other)
    let similarAmountsCount = 0;
    for (let i = 0; i < amounts.length; i++) {
        for (let j = i + 1; j < amounts.length; j++) {
            const diff = Math.abs(amounts[i] - amounts[j]);
            const avgAmount = (amounts[i] + amounts[j]) / 2;
            if (diff / avgAmount < 0.01) {
                // Within 1%
                similarAmountsCount++;
            }
        }
    }
    // Detect potential structuring (many small amounts under common thresholds)
    const smallAmountThreshold = 0.1; // 0.1 BTC
    const smallAmountsCount = amounts.filter(amount => amount < smallAmountThreshold).length;
    const potentialStructuring = smallAmountsCount > amounts.length * 0.7; // 70% small amounts
    // Detect potential splitting (one large amount split into many smaller ones)
    const potentialSplitting = amounts.length > 5 && maxAmount > averageAmount * 3;
    return {
        totalAmount,
        averageAmount,
        maxAmount,
        minAmount,
        roundAmountsCount,
        similarAmountsCount,
        potentialStructuring,
        potentialSplitting,
    };
}
/**
 * Analyzes address reuse patterns
 */
function analyzeAddressReuse(transactions) {
    const fromAddresses = {};
    const toAddresses = {};
    for (const tx of transactions) {
        fromAddresses[tx.fromAddress] = (fromAddresses[tx.fromAddress] || 0) + 1;
        toAddresses[tx.toAddress] = (toAddresses[tx.toAddress] || 0) + 1;
    }
    const reusedFromAddresses = Object.fromEntries(Object.entries(fromAddresses).filter(([_, count]) => count > 1));
    const reusedToAddresses = Object.fromEntries(Object.entries(toAddresses).filter(([_, count]) => count > 1));
    const addressReuseDetected = Object.keys(reusedFromAddresses).length > 0 || Object.keys(reusedToAddresses).length > 0;
    return {
        uniqueFromAddresses: Object.keys(fromAddresses).length,
        uniqueToAddresses: Object.keys(toAddresses).length,
        reusedFromAddresses,
        reusedToAddresses,
        addressReuseDetected,
    };
}
/**
 * Generates risk indicators summary
 */
function generateRiskIndicators(transactions, addressAnalysis, maxDepth) {
    const riskFactors = [];
    let riskScore = 0;
    const totalAmount = transactions.reduce((sum, tx) => sum + tx.amountBtc, 0);
    const transactionCount = transactions.length;
    // High volume risk
    if (totalAmount > 10) {
        riskFactors.push('high_transaction_volume');
        riskScore += 2;
    }
    // High transaction count risk
    if (transactionCount > 50) {
        riskFactors.push('high_transaction_count');
        riskScore += 1;
    }
    // Deep investigation risk
    if (maxDepth > 3) {
        riskFactors.push('deep_investigation_required');
        riskScore += 1;
    }
    // High-risk addresses
    const highRiskAddresses = addressAnalysis.filter(addr => addr.riskScore && addr.riskScore > 7);
    if (highRiskAddresses.length > 0) {
        riskFactors.push('high_risk_addresses');
        riskScore += 3;
    }
    const riskLevel = calculateRiskLevel(riskScore);
    return {
        riskScore,
        riskLevel,
        riskFactors,
        totalAmount,
        transactionCount,
        maxDepth,
    };
}
/**
 * Formats suspicious activity results for consistency
 */
function formatSuspiciousActivityResult(detected, severity, confidence, details = {}) {
    return {
        detected,
        severity: Math.max(0, Math.min(5, severity)), // Clamp between 0-5
        confidence: Math.max(0, Math.min(1, confidence)), // Clamp between 0-1
        details,
    };
}
//# sourceMappingURL=shared-analysis.js.map