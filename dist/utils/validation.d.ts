/**
 * Validates Bitcoin address format
 */
export declare function validateBitcoinAddress(address: string): boolean;
/**
 * Determines the type of Bitcoin address
 */
export declare function getBitcoinAddressType(address: string): 'legacy' | 'segwit' | 'taproot' | 'unknown';
/**
 * Validates Bitcoin transaction ID format
 */
export declare function validateTransactionId(txid: string): boolean;
/**
 * Validates investigation depth parameter
 */
export declare function validateDepth(depth: number): boolean;
/**
 * Validates BTC amount
 */
export declare function validateBtcAmount(amount: number): boolean;
/**
 * Sanitizes user input to prevent injection attacks
 */
export declare function sanitizeInput(input: string): string;
/**
 * Validates email format (for contact info)
 */
export declare function validateEmail(email: string): boolean;
/**
 * Validates date format (ISO 8601)
 */
export declare function validateDate(dateString: string): boolean;
/**
 * Validates investigation configuration
 */
export declare function validateConfig(config: any): {
    valid: boolean;
    errors: string[];
};
/**
 * Validates user input for investigation
 */
export declare function validateUserInput(input: any): {
    valid: boolean;
    errors: string[];
};
/**
 * Formats validation errors for display
 */
export declare function formatValidationErrors(errors: string[]): string;
//# sourceMappingURL=validation.d.ts.map