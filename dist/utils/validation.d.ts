export { validateBitcoinAddress, getBitcoinAddressType, validateTransactionId, validateDepth, validateBtcAmount, validateEmail, validateDate, formatValidationErrors, sanitizeInput, } from './shared-validation';
/**
 * Validates investigation configuration
 */
export declare function validateConfig(config: any): {
    valid: boolean;
    errors: string[];
};
/**
 * Validates user input for investigation
 */
export declare function validateUserInput(input: any): {
    valid: boolean;
    errors: string[];
};
//# sourceMappingURL=validation.d.ts.map