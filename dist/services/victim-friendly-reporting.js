"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VictimFriendlyReportingService = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const date_fns_1 = require("date-fns");
const logger_1 = require("../utils/logger");
class VictimFriendlyReportingService {
    constructor(outputDirectory) {
        this.outputDirectory = outputDirectory;
    }
    /**
     * Generate a comprehensive victim-friendly report
     */
    async generateVictimFriendlyReport(results, enhancedRisk, advancedTracking) {
        logger_1.logger.info('Generating victim-friendly report', {
            investigationId: results.investigationId,
        });
        const report = {
            executiveSummary: this.createExecutiveSummary(results, enhancedRisk),
            fundTracing: this.createFundTracingSection(results, advancedTracking),
            actionableSteps: this.createActionableSteps(results, enhancedRisk),
            legalGuidance: this.createLegalGuidance(results, enhancedRisk),
            technicalDetails: this.createTechnicalDetails(results, enhancedRisk),
            resources: this.createResourcesSection(),
        };
        // Generate HTML report
        const htmlReport = this.generateHTMLReport(report, results);
        const filename = path_1.default.join(this.outputDirectory, `victim_report_${results.investigationId}_${(0, date_fns_1.format)(new Date(), 'yyyyMMdd_HHmmss')}.html`);
        fs_1.default.writeFileSync(filename, htmlReport);
        // Also generate a simplified text version
        const textReport = this.generateTextReport(report, results);
        const textFilename = path_1.default.join(this.outputDirectory, `victim_report_${results.investigationId}_${(0, date_fns_1.format)(new Date(), 'yyyyMMdd_HHmmss')}.txt`);
        fs_1.default.writeFileSync(textFilename, textReport);
        logger_1.logger.info('Victim-friendly report generated', {
            htmlFile: filename,
            textFile: textFilename,
        });
        return filename;
    }
    createExecutiveSummary(results, enhancedRisk) {
        const totalAmount = results.basicResults.totalAmount;
        const transactionCount = results.basicResults.transactionCount;
        const riskLevel = enhancedRisk?.finalRiskLevel || 'UNKNOWN';
        let whatHappened = `You sent ${totalAmount.toFixed(4)} Bitcoin to a scammer. `;
        whatHappened += `Our investigation traced your funds through ${transactionCount} transactions across ${results.basicResults.addressCount} different Bitcoin addresses.`;
        let whereYourMoneyWent = 'Your Bitcoin was moved through multiple addresses, ';
        if (transactionCount > 5) {
            whereYourMoneyWent += 'indicating the scammer is trying to hide the money trail. ';
        }
        if (enhancedRisk?.threatIndicators.some(ti => ti.id === 'mixing-service-usage')) {
            whereYourMoneyWent +=
                'The funds were processed through mixing services to obscure their path. ';
        }
        if (enhancedRisk?.threatIndicators.some(ti => ti.id === 'exchange-interaction')) {
            whereYourMoneyWent +=
                'Some funds appear to have reached cryptocurrency exchanges where they could be converted to cash.';
        }
        else {
            whereYourMoneyWent += 'The final destination of your funds is still being traced.';
        }
        const chanceOfRecovery = this.assessRecoveryChance(results, enhancedRisk);
        const immediateActions = [
            'Report the scam to your local police',
            "File a complaint with the FBI's IC3 (if in the US)",
            'Document all communications with the scammer',
            'Secure your other accounts and change passwords',
        ];
        const keyFindings = [
            `Total amount lost: ${totalAmount.toFixed(4)} BTC`,
            `Number of transactions traced: ${transactionCount}`,
            `Risk level: ${riskLevel}`,
            `Investigation status: ${results.investigationSummary.status}`,
        ];
        if (enhancedRisk?.threatIndicators.length) {
            keyFindings.push(`${enhancedRisk.threatIndicators.length} threat indicators identified`);
        }
        return {
            whatHappened,
            whereYourMoneyWent,
            chanceOfRecovery,
            immediateActions,
            keyFindings,
        };
    }
    createFundTracingSection(results, advancedTracking) {
        const firstTx = results.detailedTransactions[0];
        const lastTx = results.detailedTransactions[results.detailedTransactions.length - 1];
        const yourTransaction = {
            amount: firstTx?.amountBtc || 0,
            date: firstTx?.timestamp || '',
            toAddress: firstTx?.toAddress || '',
            status: firstTx?.confirmations ? 'Confirmed' : 'Pending',
            explanation: `This is the transaction where you sent ${firstTx?.amountBtc.toFixed(4)} Bitcoin to the scammer's address. The transaction is permanently recorded on the Bitcoin blockchain.`,
        };
        const followTheTrail = {
            totalHops: results.detailedTransactions.length,
            finalDestinations: this.identifyFinalDestinations(results.detailedTransactions),
            currentStatus: this.determineCurrentStatus(results.detailedTransactions),
            trailExplanation: this.explainTrail(results.detailedTransactions),
        };
        const suspiciousActivity = {
            detected: results.advancedAnalysis.suspiciousActivity.overallSuspicionScore > 5,
            activities: this.extractSuspiciousActivities(results.advancedAnalysis.suspiciousActivity),
            explanation: this.explainSuspiciousActivity(results.advancedAnalysis.suspiciousActivity),
            riskLevel: results.advancedAnalysis.riskAssessment.finalRiskLevel,
        };
        return {
            yourTransaction,
            followTheTrail,
            suspiciousActivity,
        };
    }
    createActionableSteps(results, enhancedRisk) {
        const immediate = [
            {
                title: 'Report to Authorities',
                steps: [
                    'File a police report with your local law enforcement',
                    "Submit a complaint to the FBI's Internet Crime Complaint Center (IC3)",
                    "Report to your country's cybercrime unit",
                    'Keep all reference numbers and case IDs',
                ],
                timeframe: 'Within 24 hours',
                importance: 'CRITICAL',
            },
            {
                title: 'Secure Your Accounts',
                steps: [
                    'Change passwords on all cryptocurrency accounts',
                    'Enable two-factor authentication everywhere possible',
                    'Check for unauthorized access to your accounts',
                    'Monitor your credit reports for suspicious activity',
                ],
                timeframe: 'Within 24 hours',
                importance: 'CRITICAL',
            },
        ];
        const shortTerm = [
            {
                title: 'Gather Evidence',
                steps: [
                    'Save all communications with the scammer',
                    "Screenshot the scammer's website or social media",
                    'Document the payment method you used',
                    'Keep records of this investigation report',
                ],
                timeframe: 'Within 1 week',
                importance: 'HIGH',
            },
            {
                title: 'Monitor the Blockchain',
                steps: [
                    "Set up alerts for the scammer's addresses",
                    'Check for any movement of your funds',
                    'Document any new transactions',
                    'Share updates with law enforcement',
                ],
                timeframe: 'Ongoing for 30 days',
                importance: 'MEDIUM',
            },
        ];
        const longTerm = [
            {
                title: 'Legal Action',
                steps: [
                    'Consult with a lawyer specializing in cryptocurrency fraud',
                    'Consider joining class action lawsuits if available',
                    'Explore civil recovery options',
                    'Stay informed about law enforcement progress',
                ],
                timeframe: '1-6 months',
                importance: 'MEDIUM',
            },
            {
                title: 'Prevention and Recovery',
                steps: [
                    'Learn about cryptocurrency security best practices',
                    'Join victim support groups',
                    'Share your experience to help others',
                    'Consider tax implications of the loss',
                ],
                timeframe: 'Ongoing',
                importance: 'MEDIUM',
            },
        ];
        return { immediate, shortTerm, longTerm };
    }
    createLegalGuidance(results, enhancedRisk) {
        return {
            reportingRequirements: [
                'File a police report in your jurisdiction',
                'Report to national cybercrime authorities',
                'Consider reporting to financial regulators',
                'Document everything for potential legal proceedings',
            ],
            evidenceToGather: [
                'All communications with the scammer',
                'Screenshots of websites or advertisements',
                'Transaction records and receipts',
                'This blockchain investigation report',
                'Any promises or guarantees made by the scammer',
            ],
            lawEnforcementContacts: [
                'Local Police Department',
                'FBI Internet Crime Complaint Center (IC3) - if in US',
                'National Cyber Security Centre - if in UK',
                "Your country's cybercrime reporting center",
            ],
            legalOptions: [
                'Criminal prosecution through law enforcement',
                'Civil lawsuit against known perpetrators',
                'Insurance claims if applicable',
                'Regulatory complaints against platforms used',
            ],
        };
    }
    createTechnicalDetails(results, enhancedRisk) {
        const transactionSummary = `Your ${results.basicResults.totalAmount.toFixed(4)} Bitcoin was traced through ${results.basicResults.transactionCount} transactions across ${results.basicResults.addressCount} addresses. The investigation reached a maximum depth of ${results.investigationSummary.keyMetrics.maximumDepth} transaction hops.`;
        const riskAssessment = `Risk Level: ${results.advancedAnalysis.riskAssessment.finalRiskLevel}. ` +
            `This assessment is based on transaction patterns, timing analysis, and known suspicious indicators. ` +
            `${enhancedRisk?.threatIndicators.length || 0} specific threat indicators were identified.`;
        const technicalEvidence = [
            `Investigation ID: ${results.investigationId}`,
            `Initial Transaction: ${results.inputParameters.initialTxid}`,
            `Target Address: ${results.inputParameters.targetAddress}`,
            `Analysis Date: ${results.timestamp}`,
            `Evidence Items: ${results.evidencePackage.length}`,
        ];
        return {
            simplified: true,
            transactionSummary,
            riskAssessment,
            technicalEvidence,
        };
    }
    createResourcesSection() {
        return {
            supportGroups: [
                'Cryptocurrency Fraud Victims Support Group',
                'Scam Survivors Network',
                'Better Business Bureau Scam Tracker',
                'Reddit r/CryptocurrencyFraud community',
            ],
            legalAid: [
                'Legal Aid Society (for low-income victims)',
                'State Bar Association referral services',
                'Cryptocurrency law specialists',
                'Consumer protection attorneys',
            ],
            cybersecurityHelp: [
                'National Cyber Security Alliance',
                'SANS Institute security resources',
                'Cryptocurrency security guides',
                'Identity theft protection services',
            ],
            preventionTips: [
                "Never send cryptocurrency to someone you haven't met in person",
                'Be suspicious of guaranteed returns or "get rich quick" schemes',
                'Verify the legitimacy of any investment platform',
                'Use only reputable cryptocurrency exchanges',
                'Enable all available security features on your accounts',
            ],
        };
    }
    // Helper methods
    assessRecoveryChance(results, enhancedRisk) {
        let score = 0;
        // Recent transactions increase chances
        const recentTxs = results.detailedTransactions.filter(tx => {
            const txTime = new Date(tx.timestamp).getTime();
            const weekAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
            return txTime > weekAgo;
        });
        if (recentTxs.length > 0)
            score += 2;
        // Exchange interactions might help
        if (enhancedRisk?.threatIndicators.some(ti => ti.id === 'exchange-interaction')) {
            score += 1;
        }
        // Mixing services reduce chances
        if (enhancedRisk?.threatIndicators.some(ti => ti.id === 'mixing-service-usage')) {
            score -= 2;
        }
        // High transaction count reduces chances
        if (results.basicResults.transactionCount > 10) {
            score -= 1;
        }
        if (score >= 2)
            return 'GOOD';
        if (score >= 1)
            return 'MODERATE';
        if (score >= 0)
            return 'LOW';
        return 'VERY_LOW';
    }
    identifyFinalDestinations(transactions) {
        // Get the last few transactions as potential final destinations
        const lastTransactions = transactions.slice(-3);
        return lastTransactions.map(tx => tx.toAddress);
    }
    determineCurrentStatus(transactions) {
        const lastTx = transactions[transactions.length - 1];
        if (!lastTx)
            return 'No transactions found';
        const timeSinceLastTx = Date.now() - new Date(lastTx.timestamp).getTime();
        const hoursAgo = Math.floor(timeSinceLastTx / (1000 * 60 * 60));
        if (hoursAgo < 24) {
            return `Last movement was ${hoursAgo} hours ago`;
        }
        else {
            const daysAgo = Math.floor(hoursAgo / 24);
            return `Last movement was ${daysAgo} days ago`;
        }
    }
    explainTrail(transactions) {
        if (transactions.length <= 1) {
            return 'Your funds have not been moved from the initial address yet.';
        }
        let explanation = `Your Bitcoin has been moved ${transactions.length} times. `;
        if (transactions.length > 5) {
            explanation += 'This suggests the scammer is actively trying to hide the money trail. ';
        }
        const uniqueAddresses = new Set(transactions.map(tx => tx.toAddress)).size;
        explanation += `The funds have passed through ${uniqueAddresses} different addresses. `;
        const avgAmount = transactions.reduce((sum, tx) => sum + tx.amountBtc, 0) / transactions.length;
        if (avgAmount < transactions[0].amountBtc * 0.5) {
            explanation +=
                'The amounts are getting smaller, which might indicate the funds are being split up or fees are being deducted.';
        }
        return explanation;
    }
    extractSuspiciousActivities(suspiciousActivity) {
        const activities = [];
        if (suspiciousActivity.mixingServices?.detected) {
            activities.push('Cryptocurrency mixing services detected');
        }
        if (suspiciousActivity.exchangeDeposits?.detected) {
            activities.push('Deposits to cryptocurrency exchanges detected');
        }
        if (suspiciousActivity.peelChains?.detected) {
            activities.push('Peel chain money laundering pattern detected');
        }
        if (suspiciousActivity.consolidationPatterns?.detected) {
            activities.push('Fund consolidation patterns detected');
        }
        return activities;
    }
    explainSuspiciousActivity(suspiciousActivity) {
        if (suspiciousActivity.overallSuspicionScore < 3) {
            return 'The transaction patterns appear relatively normal with minimal suspicious indicators.';
        }
        let explanation = 'Several suspicious patterns have been detected in the transaction flow. ';
        if (suspiciousActivity.mixingServices?.detected) {
            explanation += 'The use of mixing services suggests an attempt to obscure the money trail. ';
        }
        if (suspiciousActivity.exchangeDeposits?.detected) {
            explanation +=
                'Deposits to exchanges indicate the scammer may be converting Bitcoin to cash. ';
        }
        if (suspiciousActivity.peelChains?.detected) {
            explanation += 'Peel chain patterns suggest systematic money laundering techniques. ';
        }
        explanation +=
            'These patterns are commonly associated with professional cryptocurrency fraud operations.';
        return explanation;
    }
    generateHTMLReport(report, results) {
        // This would generate a comprehensive HTML report
        // For brevity, returning a simplified version
        return `
<!DOCTYPE html>
<html>
<head>
    <title>Bitcoin Scam Investigation Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
        .section { margin: 30px 0; }
        .critical { color: #d32f2f; font-weight: bold; }
        .high { color: #f57c00; font-weight: bold; }
        .medium { color: #1976d2; }
        .steps { background: #f9f9f9; padding: 15px; border-left: 4px solid #2196f3; }
        .warning { background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Bitcoin Scam Investigation Report</h1>
        <p><strong>Investigation ID:</strong> ${results.investigationId}</p>
        <p><strong>Report Generated:</strong> ${new Date().toLocaleString()}</p>
    </div>

    <div class="section">
        <h2>📋 Executive Summary</h2>
        <h3>What Happened</h3>
        <p>${report.executiveSummary.whatHappened}</p>

        <h3>Where Your Money Went</h3>
        <p>${report.executiveSummary.whereYourMoneyWent}</p>

        <h3>Chance of Recovery: ${report.executiveSummary.chanceOfRecovery}</h3>

        <div class="warning">
            <h4>🚨 Immediate Actions Required</h4>
            <ul>
                ${report.executiveSummary.immediateActions.map(action => `<li>${action}</li>`).join('')}
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>💰 Fund Tracing</h2>
        <h3>Your Original Transaction</h3>
        <p><strong>Amount:</strong> ${report.fundTracing.yourTransaction.amount.toFixed(4)} BTC</p>
        <p><strong>Date:</strong> ${new Date(report.fundTracing.yourTransaction.date).toLocaleString()}</p>
        <p><strong>To Address:</strong> ${report.fundTracing.yourTransaction.toAddress}</p>
        <p>${report.fundTracing.yourTransaction.explanation}</p>

        <h3>Following the Trail</h3>
        <p><strong>Total Hops:</strong> ${report.fundTracing.followTheTrail.totalHops}</p>
        <p><strong>Current Status:</strong> ${report.fundTracing.followTheTrail.currentStatus}</p>
        <p>${report.fundTracing.followTheTrail.trailExplanation}</p>
    </div>

    <div class="section">
        <h2>⚡ Action Steps</h2>
        <h3 class="critical">Immediate Actions (24 hours)</h3>
        ${report.actionableSteps.immediate
            .map(step => `
            <div class="steps">
                <h4>${step.title}</h4>
                <ul>${step.steps.map(s => `<li>${s}</li>`).join('')}</ul>
            </div>
        `)
            .join('')}

        <h3 class="high">Short-term Actions (1 week)</h3>
        ${report.actionableSteps.shortTerm
            .map(step => `
            <div class="steps">
                <h4>${step.title}</h4>
                <ul>${step.steps.map(s => `<li>${s}</li>`).join('')}</ul>
            </div>
        `)
            .join('')}
    </div>

    <div class="section">
        <h2>📞 Resources and Support</h2>
        <h3>Support Groups</h3>
        <ul>${report.resources.supportGroups.map(group => `<li>${group}</li>`).join('')}</ul>

        <h3>Legal Aid</h3>
        <ul>${report.resources.legalAid.map(aid => `<li>${aid}</li>`).join('')}</ul>

        <h3>Prevention Tips</h3>
        <ul>${report.resources.preventionTips.map(tip => `<li>${tip}</li>`).join('')}</ul>
    </div>

    <div class="section">
        <h2>📄 Technical Summary</h2>
        <p>${report.technicalDetails.transactionSummary}</p>
        <p>${report.technicalDetails.riskAssessment}</p>
    </div>

    <footer style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #ccc; font-size: 12px; color: #666;">
        <p>This report was generated by the Bitcoin Forensic Investigation Tool v3.0.
        For technical support or questions about this report, please consult with qualified
        legal and technical professionals.</p>
    </footer>
</body>
</html>`;
    }
    generateTextReport(report, results) {
        return `
BITCOIN SCAM INVESTIGATION REPORT
=================================

Investigation ID: ${results.investigationId}
Report Generated: ${new Date().toLocaleString()}

EXECUTIVE SUMMARY
================

What Happened:
${report.executiveSummary.whatHappened}

Where Your Money Went:
${report.executiveSummary.whereYourMoneyWent}

Chance of Recovery: ${report.executiveSummary.chanceOfRecovery}

IMMEDIATE ACTIONS REQUIRED:
${report.executiveSummary.immediateActions.map(action => `• ${action}`).join('\n')}

FUND TRACING
===========

Your Original Transaction:
• Amount: ${report.fundTracing.yourTransaction.amount.toFixed(4)} BTC
• Date: ${new Date(report.fundTracing.yourTransaction.date).toLocaleString()}
• To Address: ${report.fundTracing.yourTransaction.toAddress}

${report.fundTracing.yourTransaction.explanation}

Following the Trail:
• Total Hops: ${report.fundTracing.followTheTrail.totalHops}
• Current Status: ${report.fundTracing.followTheTrail.currentStatus}

${report.fundTracing.followTheTrail.trailExplanation}

ACTION STEPS
===========

IMMEDIATE (24 hours):
${report.actionableSteps.immediate
            .map(step => `
${step.title}:
${step.steps.map(s => `• ${s}`).join('\n')}
`)
            .join('\n')}

SHORT-TERM (1 week):
${report.actionableSteps.shortTerm
            .map(step => `
${step.title}:
${step.steps.map(s => `• ${s}`).join('\n')}
`)
            .join('\n')}

RESOURCES AND SUPPORT
====================

Support Groups:
${report.resources.supportGroups.map(group => `• ${group}`).join('\n')}

Legal Aid:
${report.resources.legalAid.map(aid => `• ${aid}`).join('\n')}

Prevention Tips:
${report.resources.preventionTips.map(tip => `• ${tip}`).join('\n')}

TECHNICAL SUMMARY
================

${report.technicalDetails.transactionSummary}

${report.technicalDetails.riskAssessment}

Technical Evidence:
${report.technicalDetails.technicalEvidence.map(evidence => `• ${evidence}`).join('\n')}

---
This report was generated by the Bitcoin Forensic Investigation Tool v3.0.
For technical support or questions about this report, please consult with
qualified legal and technical professionals.
`;
    }
}
exports.VictimFriendlyReportingService = VictimFriendlyReportingService;
//# sourceMappingURL=victim-friendly-reporting.js.map