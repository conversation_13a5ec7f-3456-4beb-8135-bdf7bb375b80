import { TransactionInfo, AddressInfo } from '../types';
import { BitcoinAPIService } from './api';
export interface WalletCluster {
    clusterId: string;
    addresses: string[];
    totalBalance: number;
    transactionCount: number;
    firstSeen: string;
    lastSeen: string;
    riskScore: number;
    clusteringEvidence: {
        commonInputs: number;
        timingCorrelations: number;
        amountPatterns: number;
        addressReuse: number;
    };
}
export interface MixingServiceDetection {
    serviceName: string;
    confidence: number;
    addresses: string[];
    transactions: string[];
    patterns: {
        equalOutputs: boolean;
        multipleInputs: boolean;
        timingPattern: boolean;
        knownService: boolean;
    };
}
export interface FundFlow {
    sourceAddress: string;
    destinationAddress: string;
    amount: number;
    path: {
        txid: string;
        depth: number;
        timestamp: string;
    }[];
    confidence: number;
    obfuscationLevel: 'NONE' | 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME';
}
export interface RealTimeBalance {
    address: string;
    balance: number;
    unconfirmedBalance: number;
    lastUpdated: string;
    transactionCount: number;
    recentActivity: {
        txid: string;
        amount: number;
        type: 'incoming' | 'outgoing';
        timestamp: string;
        confirmations: number;
    }[];
}
export interface AdvancedTrackingResults {
    walletClusters: WalletCluster[];
    mixingServices: MixingServiceDetection[];
    fundFlows: FundFlow[];
    realTimeBalances: RealTimeBalance[];
    complexPatterns: {
        peelChains: any[];
        consolidationEvents: any[];
        splitTransactions: any[];
        circularTransactions: any[];
    };
    exchangeInteractions: {
        knownExchanges: string[];
        suspectedExchanges: string[];
        depositAddresses: string[];
        withdrawalPatterns: any[];
    };
}
export declare class AdvancedBitcoinTrackingService {
    private apiService;
    private knownMixingServices;
    private knownExchanges;
    private addressClusters;
    constructor(apiService: BitcoinAPIService);
    private initializeKnownServices;
    /**
     * Perform comprehensive advanced tracking analysis
     */
    performAdvancedTracking(transactions: TransactionInfo[], addresses: AddressInfo[]): Promise<AdvancedTrackingResults>;
    /**
     * Analyze wallet clustering using multiple heuristics
     */
    private analyzeWalletClusters;
    /**
     * Detect mixing services and privacy tools
     */
    private detectMixingServices;
    /**
     * Trace complex fund flows through the blockchain
     */
    private traceFundFlows;
    /**
     * Get real-time balance information for addresses
     */
    private getRealTimeBalances;
    /**
     * Detect complex transaction patterns
     */
    private detectComplexPatterns;
    /**
     * Analyze interactions with cryptocurrency exchanges
     */
    private analyzeExchangeInteractions;
    private getTransactionInputAddresses;
    private getTransactionOutputs;
    private identifyChangeAddress;
    private calculateAmountRoundness;
    private mergeAddressesIntoCluster;
    private applyTimingCorrelationHeuristic;
    private calculateClusterStatistics;
    private calculateClusterRiskScore;
    private recordMixingServiceDetection;
    private hasEqualOutputPattern;
    private hasMixingTimingPattern;
    private calculateMixingConfidence;
    private createFundFlow;
    private findIndirectFundFlow;
    private detectPeelChains;
    private detectConsolidationEvents;
    private detectSplitTransactions;
    private detectCircularTransactions;
    private isLikelyExchangeAddress;
    private isLikelyDepositAddress;
    private detectWithdrawalPatterns;
}
//# sourceMappingURL=advanced-tracking.d.ts.map