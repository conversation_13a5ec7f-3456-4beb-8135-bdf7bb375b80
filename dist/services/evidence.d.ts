import { EvidenceItem, TransactionInfo, AddressInfo } from '../types';
export interface EvidenceCategory {
    id: string;
    name: string;
    description: string;
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    tags: string[];
}
export interface EvidenceIntegrityCheck {
    evidenceId: string;
    originalHash: string;
    currentHash: string;
    isValid: boolean;
    timestamp: string;
    verifiedBy: string;
}
export interface DigitalSignature {
    algorithm: string;
    signature: string;
    publicKey: string;
    timestamp: string;
    signedBy: string;
}
export interface EnhancedEvidenceItem extends EvidenceItem {
    category: EvidenceCategory;
    digitalSignature?: DigitalSignature;
    integrityChecks: EvidenceIntegrityCheck[];
    relatedEvidence: string[];
    metadata: {
        source: string;
        collectionMethod: string;
        jurisdiction: string;
        legalBasis: string;
        retentionPeriod: string;
        accessLevel: 'PUBLIC' | 'RESTRICTED' | 'CONFIDENTIAL' | 'SECRET';
    };
    attachments: {
        id: string;
        filename: string;
        mimeType: string;
        size: number;
        hash: string;
    }[];
}
export declare class EnhancedEvidenceService {
    private evidenceItems;
    private categories;
    private investigationId;
    private outputDirectory;
    constructor(investigationId: string, outputDirectory: string);
    private initializeDefaultCategories;
    /**
     * Create enhanced evidence item with comprehensive metadata
     */
    createEvidenceItem(evidenceType: string, description: string, data: Record<string, any>, categoryId?: string, metadata?: Partial<EnhancedEvidenceItem['metadata']>): EnhancedEvidenceItem;
    /**
     * Update chain of custody with detailed tracking
     */
    updateChainOfCustody(evidenceId: string, action: string, description: string, userId?: string, additionalData?: Record<string, any>): boolean;
    /**
     * Verify evidence integrity
     */
    verifyEvidenceIntegrity(evidenceId: string, verifiedBy?: string): EvidenceIntegrityCheck;
    /**
     * Link related evidence items
     */
    linkEvidence(evidenceId1: string, evidenceId2: string, relationship: string): boolean;
    private createDataHash;
    /**
     * Add attachment to evidence item
     */
    addAttachment(evidenceId: string, filename: string, content: Buffer, mimeType?: string): boolean;
    /**
     * Create evidence from transaction data
     */
    createTransactionEvidence(transaction: TransactionInfo): EnhancedEvidenceItem;
    /**
     * Create evidence from address data
     */
    createAddressEvidence(address: AddressInfo): EnhancedEvidenceItem;
    /**
     * Export comprehensive evidence package
     */
    exportEvidencePackage(): Promise<string>;
    /**
     * Generate audit report
     */
    generateAuditReport(): {
        summary: any;
        evidenceItems: any[];
        integrityStatus: any;
        chainOfCustodyReport: any[];
    };
    private createPackageHash;
    /**
     * Get evidence statistics
     */
    getStatistics(): {
        totalEvidence: number;
        byCategory: Record<string, number>;
        byType: Record<string, number>;
        integrityStatus: {
            valid: number;
            invalid: number;
        };
    };
    /**
     * Get all evidence items
     */
    getAllEvidence(): EnhancedEvidenceItem[];
    /**
     * Get evidence by ID
     */
    getEvidence(evidenceId: string): EnhancedEvidenceItem | undefined;
    /**
     * Search evidence by criteria
     */
    searchEvidence(criteria: {
        category?: string;
        type?: string;
        description?: string;
        tags?: string[];
    }): EnhancedEvidenceItem[];
}
//# sourceMappingURL=evidence.d.ts.map