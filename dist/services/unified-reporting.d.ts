import { InvestigationResults } from '../types';
import { EnhancedRiskAssessment } from './enhanced-risk-assessment';
import { AdvancedTrackingResults } from './advanced-tracking';
export interface ReportOptions {
    includeVictimFriendly?: boolean;
    includeTechnical?: boolean;
    includeVisualizations?: boolean;
    outputFormats?: ('pdf' | 'html' | 'text' | 'json' | 'csv')[];
}
export interface VictimFriendlyReport {
    executiveSummary: {
        whatHappened: string;
        whereYourMoneyWent: string;
        chanceOfRecovery: 'VERY_LOW' | 'LOW' | 'MODERATE' | 'GOOD' | 'HIGH';
        immediateActions: string[];
        keyFindings: string[];
    };
    fundTracing: {
        yourTransaction: {
            amount: number;
            date: string;
            toAddress: string;
            status: string;
            explanation: string;
        };
        followTheTrail: {
            totalHops: number;
            finalDestinations: string[];
            currentStatus: string;
            trailExplanation: string;
        };
        suspiciousActivity: {
            detected: boolean;
            activities: string[];
            explanation: string;
            riskLevel: string;
        };
    };
    actionableSteps: {
        immediate: Array<{
            title: string;
            steps: string[];
            timeframe: string;
            importance: 'CRITICAL' | 'HIGH' | 'MEDIUM';
        }>;
        shortTerm: Array<{
            title: string;
            steps: string[];
            timeframe: string;
            importance: 'CRITICAL' | 'HIGH' | 'MEDIUM';
        }>;
        longTerm: Array<{
            title: string;
            steps: string[];
            timeframe: string;
            importance: 'CRITICAL' | 'HIGH' | 'MEDIUM';
        }>;
    };
    legalGuidance: {
        reportingRequirements: string[];
        evidenceToGather: string[];
        lawEnforcementContacts: string[];
        legalOptions: string[];
    };
    technicalDetails: {
        simplified: boolean;
        transactionSummary: string;
        riskAssessment: string;
        technicalEvidence: string[];
    };
    resources: {
        supportGroups: string[];
        legalAid: string[];
        cybersecurityHelp: string[];
        preventionTips: string[];
    };
}
export declare class UnifiedReportingService {
    private outputDirectory;
    private visualizationService;
    constructor(outputDirectory: string);
    /**
     * Generate comprehensive reports based on options
     */
    generateReports(results: InvestigationResults, enhancedRisk?: EnhancedRiskAssessment, advancedTracking?: AdvancedTrackingResults, options?: ReportOptions): Promise<{
        victimReport?: string;
        technicalReports: string[];
        allFiles: string[];
    }>;
    /**
     * Generate victim-friendly report
     */
    private generateVictimFriendlyReport;
    /**
     * Generate technical PDF report
     */
    private generatePDFReport;
    /**
     * Generate technical HTML report
     */
    private generateTechnicalHTMLReport;
    /**
     * Generate JSON evidence package
     */
    private generateJSONEvidence;
    /**
     * Generate CSV data export
     */
    private generateCSVExport;
    /**
     * Generate text report
     */
    private generateTextReport;
    private createExecutiveSummary;
    private createFundTracingSection;
    private createActionableSteps;
    private createLegalGuidance;
    private createTechnicalDetails;
    private createResourcesSection;
    private assessRecoveryChance;
    private identifyFinalDestinations;
    private determineCurrentStatus;
    private explainTrail;
    private extractSuspiciousActivities;
    private explainSuspiciousActivity;
    private calculateChecksum;
    private addPDFCoverPage;
    private addPDFExecutiveSummary;
    private addPDFInvestigationDetails;
    private generateTechnicalHTMLTemplate;
    private generateVictimHTMLReport;
    private generateVictimTextReport;
}
//# sourceMappingURL=unified-reporting.d.ts.map