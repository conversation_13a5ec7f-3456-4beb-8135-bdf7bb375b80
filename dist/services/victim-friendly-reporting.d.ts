import { InvestigationResults } from '../types';
import { EnhancedRiskAssessment } from './enhanced-risk-assessment';
import { AdvancedTrackingResults } from './advanced-tracking';
export interface VictimFriendlyReport {
    executiveSummary: {
        whatHappened: string;
        whereYourMoneyWent: string;
        chanceOfRecovery: 'VERY_LOW' | 'LOW' | 'MODERATE' | 'GOOD' | 'HIGH';
        immediateActions: string[];
        keyFindings: string[];
    };
    fundTracing: {
        yourTransaction: {
            amount: number;
            date: string;
            toAddress: string;
            status: string;
            explanation: string;
        };
        followTheTrail: {
            totalHops: number;
            finalDestinations: string[];
            currentStatus: string;
            trailExplanation: string;
        };
        suspiciousActivity: {
            detected: boolean;
            activities: string[];
            explanation: string;
            riskLevel: string;
        };
    };
    actionableSteps: {
        immediate: {
            title: string;
            steps: string[];
            timeframe: string;
            importance: 'CRITICAL' | 'HIGH' | 'MEDIUM';
        }[];
        shortTerm: {
            title: string;
            steps: string[];
            timeframe: string;
            importance: 'CRITICAL' | 'HIGH' | 'MEDIUM';
        }[];
        longTerm: {
            title: string;
            steps: string[];
            timeframe: string;
            importance: 'CRITICAL' | 'HIGH' | 'MEDIUM';
        }[];
    };
    legalGuidance: {
        reportingRequirements: string[];
        evidenceToGather: string[];
        lawEnforcementContacts: string[];
        legalOptions: string[];
    };
    technicalDetails: {
        simplified: boolean;
        transactionSummary: string;
        riskAssessment: string;
        technicalEvidence: string[];
    };
    resources: {
        supportGroups: string[];
        legalAid: string[];
        cybersecurityHelp: string[];
        preventionTips: string[];
    };
}
export declare class VictimFriendlyReportingService {
    private outputDirectory;
    constructor(outputDirectory: string);
    /**
     * Generate a comprehensive victim-friendly report
     */
    generateVictimFriendlyReport(results: InvestigationResults, enhancedRisk?: EnhancedRiskAssessment, advancedTracking?: AdvancedTrackingResults): Promise<string>;
    private createExecutiveSummary;
    private createFundTracingSection;
    private createActionableSteps;
    private createLegalGuidance;
    private createTechnicalDetails;
    private createResourcesSection;
    private assessRecoveryChance;
    private identifyFinalDestinations;
    private determineCurrentStatus;
    private explainTrail;
    private extractSuspiciousActivities;
    private explainSuspiciousActivity;
    private generateHTMLReport;
    private generateTextReport;
}
//# sourceMappingURL=victim-friendly-reporting.d.ts.map