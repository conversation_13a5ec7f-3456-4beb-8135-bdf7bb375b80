import { TransactionInfo, AddressInfo, RiskAssessment, PatternAnalysis, SuspiciousActivity } from '../types';
export interface ThreatIndicator {
    id: string;
    name: string;
    description: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    confidence: number;
    category: 'FINANCIAL' | 'TECHNICAL' | 'BEHAVIORAL' | 'REGULATORY';
    evidence: string[];
    mitigationSteps: string[];
}
export interface RiskFactor {
    factor: string;
    weight: number;
    score: number;
    description: string;
    evidence: any[];
}
export interface EnhancedRiskAssessment extends Omit<RiskAssessment, 'riskFactors'> {
    threatIndicators: ThreatIndicator[];
    riskFactors: RiskFactor[];
    riskFactorNames: string[];
    complianceFlags: {
        amlConcerns: boolean;
        sanctionsRisk: boolean;
        jurisdictionalIssues: boolean;
        reportingRequirements: string[];
    };
    investigationPriority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
    actionableIntelligence: {
        immediateActions: string[];
        investigationSteps: string[];
        legalConsiderations: string[];
        evidenceRequirements: string[];
    };
    riskTrends: {
        increasing: boolean;
        timeframe: string;
        trendFactors: string[];
    };
}
export interface SophisticatedPattern {
    patternId: string;
    name: string;
    description: string;
    confidence: number;
    complexity: 'SIMPLE' | 'MODERATE' | 'COMPLEX' | 'SOPHISTICATED';
    transactions: string[];
    addresses: string[];
    indicators: string[];
    riskImplications: string[];
}
export declare class EnhancedRiskAssessmentService {
    private threatDatabase;
    private patternLibrary;
    constructor();
    private initializeThreatDatabase;
    private initializePatternLibrary;
    /**
     * Perform comprehensive enhanced risk assessment
     */
    performEnhancedRiskAssessment(transactions: TransactionInfo[], addresses: AddressInfo[], patterns: PatternAnalysis, suspiciousActivity: SuspiciousActivity): Promise<EnhancedRiskAssessment>;
    private detectSophisticatedPatterns;
    private identifyThreatIndicators;
    private calculateDetailedRiskFactors;
    private hasLayeredObfuscation;
    private hasExchangeHopping;
    private hasMicroMixing;
    private hasVariedTiming;
    private hasQuickSuccession;
    private assessComplianceImplications;
    private calculateCompositeRiskScore;
    private determineRiskLevel;
    private calculateBaseRiskScore;
    private calculateSuspicionScore;
    private extractSuspiciousActivities;
    private generateRecommendations;
    private generateActionableIntelligence;
    private analyzeRiskTrends;
    private determineInvestigationPriority;
}
//# sourceMappingURL=enhanced-risk-assessment.d.ts.map