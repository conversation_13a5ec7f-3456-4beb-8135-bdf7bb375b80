import { InvestigationResults } from '../types';
export interface VisualizationConfig {
    outputDirectory: string;
    includeInteractive: boolean;
    includeStatic: boolean;
    theme: 'light' | 'dark' | 'auto';
    maxNodes: number;
    maxEdges: number;
}
export interface NetworkNode {
    id: string;
    label: string;
    type: 'address' | 'transaction' | 'exchange' | 'mixer' | 'unknown';
    value: number;
    level: number;
    riskScore?: number;
    metadata: {
        address?: string;
        txid?: string;
        timestamp?: string;
        confirmations?: number;
        suspicious?: boolean;
    };
}
export interface NetworkEdge {
    from: string;
    to: string;
    value: number;
    label: string;
    type: 'transaction' | 'consolidation' | 'split';
    metadata: {
        txid: string;
        timestamp: string;
        fees?: number;
        confirmations: boolean;
    };
}
export interface VisualizationData {
    nodes: NetworkNode[];
    edges: NetworkEdge[];
    timeline: TimelineEvent[];
    riskDistribution: RiskDistribution[];
    flowSummary: FlowSummary;
}
export interface TimelineEvent {
    timestamp: string;
    type: 'transaction' | 'consolidation' | 'split' | 'exchange_deposit' | 'suspicious_activity';
    amount: number;
    description: string;
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    metadata: Record<string, any>;
}
export interface RiskDistribution {
    category: string;
    count: number;
    totalAmount: number;
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
}
export interface FlowSummary {
    totalAmount: number;
    totalTransactions: number;
    uniqueAddresses: number;
    suspiciousTransactions: number;
    exchangeDeposits: number;
    mixingServices: number;
    finalDestinations: string[];
}
export declare class VisualizationService {
    private config;
    constructor(config?: Partial<VisualizationConfig>);
    /**
     * Generate comprehensive visualizations for investigation results
     */
    generateVisualizations(results: InvestigationResults, investigationId: string): Promise<{
        interactiveReport?: string;
        networkGraph?: string;
        timeline?: string;
        riskChart?: string;
        flowDiagram?: string;
    }>;
    /**
     * Prepare data for visualization
     */
    private prepareVisualizationData;
    /**
     * Generate interactive comprehensive report
     */
    private generateInteractiveReport;
    /**
     * Generate network graph visualization
     */
    private generateNetworkGraph;
    /**
     * Generate timeline visualization
     */
    private generateTimeline;
    /**
     * Generate risk chart visualization
     */
    private generateRiskChart;
    /**
     * Generate flow diagram visualization
     */
    private generateFlowDiagram;
    private formatAddressLabel;
    private determineAddressType;
    private calculateTransactionRiskLevel;
    private countExchangeDeposits;
    private countMixingServices;
    private identifyFinalDestinations;
    /**
     * Generate comprehensive interactive HTML report
     */
    private generateInteractiveHTML;
    /**
     * Generate network graph HTML
     */
    private generateNetworkGraphHTML;
    private generateTimelineHTML;
    private generateRiskChartHTML;
    private generateFlowDiagramHTML;
}
//# sourceMappingURL=visualization.d.ts.map