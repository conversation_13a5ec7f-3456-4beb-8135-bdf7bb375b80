import { InvestigationResults } from '../types';
export declare class ReportingService {
    private outputDirectory;
    constructor(outputDirectory?: string);
    /**
     * Generate comprehensive PDF report
     */
    generatePDFReport(results: InvestigationResults): Promise<string>;
    /**
     * Generate interactive HTML report
     */
    generateHTMLReport(results: InvestigationResults): Promise<string>;
    /**
     * Generate JSON evidence package
     */
    generateJSONEvidence(results: InvestigationResults): Promise<string>;
    /**
     * Generate CSV data export
     */
    generateCSVExport(results: InvestigationResults): Promise<string>;
    /**
     * Generate text report
     */
    generateTextReport(results: InvestigationResults): Promise<string>;
    private addCoverPage;
    private addExecutiveSummary;
    private addInvestigationDetails;
    private addTransactionAnalysis;
    private addRiskAssessment;
    private addEvidencePackage;
    private addAppendices;
    private generateHTMLTemplate;
    private calculateChecksum;
}
//# sourceMappingURL=reporting.d.ts.map