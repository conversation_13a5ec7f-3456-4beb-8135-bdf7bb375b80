"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BitcoinAPIService = void 0;
const axios_1 = __importDefault(require("axios"));
const logger_1 = require("../utils/logger");
class BitcoinAPIService {
    constructor(config) {
        this.requestCount = 0;
        this.lastRequestTime = 0;
        // Performance optimizations
        this.cache = new Map();
        this.batchQueue = [];
        this.batchTimer = null;
        this.CACHE_TTL = 5 * 60 * 1000; // 5 minutes
        this.BATCH_SIZE = 10;
        this.BATCH_DELAY = 100; // 100ms
        this.config = config;
        this.client = axios_1.default.create({
            baseURL: config.apiBaseUrl,
            timeout: config.requestTimeout,
            headers: {
                'User-Agent': 'Bitcoin-Forensics-Tool/3.0.0',
                Accept: 'application/json',
                Connection: 'keep-alive',
            },
            // Connection pooling for better performance
            maxRedirects: 3,
            maxContentLength: 50 * 1024 * 1024, // 50MB
        });
        this.setupInterceptors();
    }
    setupInterceptors() {
        // Request interceptor for rate limiting
        this.client.interceptors.request.use(async (config) => {
            await this.enforceRateLimit();
            this.requestCount++;
            return config;
        });
        // Response interceptor for logging and error handling
        this.client.interceptors.response.use(response => {
            (0, logger_1.logAPICall)(response.config.url || 'unknown', true, Date.now() - this.lastRequestTime);
            return response;
        }, error => {
            const duration = Date.now() - this.lastRequestTime;
            (0, logger_1.logAPICall)(error.config?.url || 'unknown', false, duration);
            if (error.response?.status === 429) {
                logger_1.logger.warn('Rate limit exceeded', {
                    url: error.config?.url,
                    retryAfter: error.response.headers['retry-after'],
                });
            }
            return Promise.reject(error);
        });
    }
    async enforceRateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        if (timeSinceLastRequest < this.config.rateLimitDelay) {
            const waitTime = this.config.rateLimitDelay - timeSinceLastRequest;
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
        this.lastRequestTime = Date.now();
    }
    /**
     * Cache management methods for performance optimization
     */
    getCacheKey(type, params) {
        return `${type}:${JSON.stringify(params)}`;
    }
    getFromCache(key) {
        const entry = this.cache.get(key);
        if (!entry)
            return null;
        if (Date.now() > entry.expiresAt) {
            this.cache.delete(key);
            return null;
        }
        return entry.data;
    }
    setCache(key, data) {
        const now = Date.now();
        this.cache.set(key, {
            data,
            timestamp: now,
            expiresAt: now + this.CACHE_TTL,
        });
    }
    clearExpiredCache() {
        const now = Date.now();
        for (const [key, entry] of this.cache.entries()) {
            if (now > entry.expiresAt) {
                this.cache.delete(key);
            }
        }
    }
    /**
     * Batch processing for improved performance
     */
    addToBatch(type, params) {
        return new Promise((resolve, reject) => {
            const id = `${type}_${Date.now()}_${Math.random()}`;
            this.batchQueue.push({ id, type, params, resolve, reject });
            if (this.batchQueue.length >= this.BATCH_SIZE) {
                this.processBatch();
            }
            else if (!this.batchTimer) {
                this.batchTimer = setTimeout(() => this.processBatch(), this.BATCH_DELAY);
            }
        });
    }
    async processBatch() {
        if (this.batchTimer) {
            clearTimeout(this.batchTimer);
            this.batchTimer = null;
        }
        const batch = this.batchQueue.splice(0, this.BATCH_SIZE);
        if (batch.length === 0)
            return;
        // Group by type for efficient processing
        const groups = batch.reduce((acc, req) => {
            if (!acc[req.type])
                acc[req.type] = [];
            acc[req.type].push(req);
            return acc;
        }, {});
        // Process each group
        for (const [type, requests] of Object.entries(groups)) {
            await this.processBatchGroup(type, requests);
        }
    }
    async processBatchGroup(type, requests) {
        for (const request of requests) {
            try {
                let result;
                switch (type) {
                    case 'transaction':
                        result = await this.getTransactionDirect(request.params.txid);
                        break;
                    case 'outspend':
                        result = await this.getOutspendDirect(request.params.txid, request.params.vout);
                        break;
                    case 'address':
                        result = await this.getAddressInfoDirect(request.params.address);
                        break;
                }
                request.resolve(result);
            }
            catch (error) {
                request.reject(error);
            }
        }
    }
    async getTransaction(txid) {
        // Check cache first
        const cacheKey = this.getCacheKey('transaction', { txid });
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            logger_1.logger.debug('Cache hit for transaction', { txid });
            return cached;
        }
        // Use batch processing for better performance
        try {
            const result = await this.addToBatch('transaction', {
                txid,
            });
            this.setCache(cacheKey, result);
            return result;
        }
        catch (error) {
            // Fallback to direct call
            return this.getTransactionDirect(txid);
        }
    }
    async getTransactionDirect(txid) {
        try {
            const response = await this.retryRequest(() => this.client.get(`/tx/${txid}`));
            const result = {
                success: true,
                data: response.data,
            };
            // Cache the result
            const cacheKey = this.getCacheKey('transaction', { txid });
            this.setCache(cacheKey, result);
            return result;
        }
        catch (error) {
            (0, logger_1.logError)(error, { txid, action: 'getTransaction' });
            if (error.response?.status === 404) {
                return {
                    success: false,
                    error: `Transaction not found: ${txid}`,
                };
            }
            if (error.response?.status === 429) {
                return {
                    success: false,
                    error: 'Rate limit exceeded',
                    rateLimited: true,
                    retryAfter: parseInt(error.response.headers['retry-after'] || '60'),
                };
            }
            return {
                success: false,
                error: `Failed to fetch transaction: ${error.message}`,
            };
        }
    }
    async getOutspend(txid, vout) {
        // Check cache first
        const cacheKey = this.getCacheKey('outspend', { txid, vout });
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            logger_1.logger.debug('Cache hit for outspend', { txid, vout });
            return cached;
        }
        // Use batch processing for better performance
        try {
            const result = await this.addToBatch('outspend', {
                txid,
                vout,
            });
            this.setCache(cacheKey, result);
            return result;
        }
        catch (error) {
            // Fallback to direct call
            return this.getOutspendDirect(txid, vout);
        }
    }
    async getOutspendDirect(txid, vout) {
        try {
            const response = await this.retryRequest(() => this.client.get(`/tx/${txid}/outspend/${vout}`));
            const result = {
                success: true,
                data: response.data,
            };
            // Cache the result
            const cacheKey = this.getCacheKey('outspend', { txid, vout });
            this.setCache(cacheKey, result);
            return result;
        }
        catch (error) {
            (0, logger_1.logError)(error, { txid, vout, action: 'getOutspend' });
            if (error.response?.status === 404) {
                return {
                    success: false,
                    error: `Outspend not found: ${txid}:${vout}`,
                };
            }
            return {
                success: false,
                error: `Failed to fetch outspend: ${error.message}`,
            };
        }
    }
    async getAddressInfo(address) {
        try {
            const response = await this.retryRequest(() => this.client.get(`/address/${address}`));
            return {
                success: true,
                data: response.data,
            };
        }
        catch (error) {
            (0, logger_1.logError)(error, { address, action: 'getAddressInfo' });
            return {
                success: false,
                error: `Failed to fetch address info: ${error.message}`,
            };
        }
    }
    async getAddressTransactions(address, lastSeenTxid) {
        try {
            const url = lastSeenTxid
                ? `/address/${address}/txs/chain/${lastSeenTxid}`
                : `/address/${address}/txs`;
            const response = await this.retryRequest(() => this.client.get(url));
            return {
                success: true,
                data: response.data,
            };
        }
        catch (error) {
            (0, logger_1.logError)(error, { address, lastSeenTxid, action: 'getAddressTransactions' });
            return {
                success: false,
                error: `Failed to fetch address transactions: ${error.message}`,
            };
        }
    }
    async retryRequest(requestFn) {
        let lastError;
        for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
            try {
                return await requestFn();
            }
            catch (error) {
                lastError = error;
                if (error.response?.status === 429) {
                    // Rate limited - wait longer
                    const retryAfter = parseInt(error.response.headers['retry-after'] || '60');
                    const waitTime = Math.min(retryAfter * 1000, 60000); // Max 60 seconds
                    logger_1.logger.warn(`Rate limited, waiting ${waitTime}ms before retry ${attempt}/${this.config.maxRetries}`);
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                    continue;
                }
                if (error.response?.status === 404) {
                    // Don't retry 404s
                    throw error;
                }
                if (attempt < this.config.maxRetries) {
                    // Exponential backoff for other errors
                    const waitTime = Math.min(1000 * Math.pow(2, attempt - 1), 10000); // Max 10 seconds
                    logger_1.logger.warn(`Request failed, retrying in ${waitTime}ms (attempt ${attempt}/${this.config.maxRetries})`);
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                }
            }
        }
        throw lastError;
    }
    getRequestCount() {
        return this.requestCount;
    }
    resetRequestCount() {
        this.requestCount = 0;
    }
    /**
     * Performance monitoring and cleanup methods
     */
    getCacheStats() {
        const size = this.cache.size;
        const memoryUsage = JSON.stringify([...this.cache.entries()]).length;
        return {
            size,
            hitRate: 0, // Would need to track hits/misses for accurate calculation
            memoryUsage,
        };
    }
    clearCache() {
        this.cache.clear();
        logger_1.logger.info('API cache cleared');
    }
    async cleanup() {
        // Clear any pending batch timer
        if (this.batchTimer) {
            clearTimeout(this.batchTimer);
            this.batchTimer = null;
        }
        // Process any remaining batch items
        if (this.batchQueue.length > 0) {
            await this.processBatch();
        }
        // Clear expired cache entries
        this.clearExpiredCache();
        logger_1.logger.info('API service cleanup completed');
    }
    async getAddressInfoDirect(address) {
        try {
            const response = await this.retryRequest(() => this.client.get(`/address/${address}`));
            const result = {
                success: true,
                data: response.data,
            };
            // Cache the result
            const cacheKey = this.getCacheKey('address', { address });
            this.setCache(cacheKey, result);
            return result;
        }
        catch (error) {
            (0, logger_1.logError)(error, { address, action: 'getAddressInfo' });
            return {
                success: false,
                error: `Failed to fetch address info: ${error.message}`,
            };
        }
    }
}
exports.BitcoinAPIService = BitcoinAPIService;
//# sourceMappingURL=api.js.map