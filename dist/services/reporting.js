"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportingService = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const pdf_lib_1 = require("pdf-lib");
const date_fns_1 = require("date-fns");
const logger_1 = require("../utils/logger");
class ReportingService {
    constructor(outputDirectory = 'investigation_results') {
        this.outputDirectory = outputDirectory;
        // Ensure output directory exists
        if (!fs_1.default.existsSync(outputDirectory)) {
            fs_1.default.mkdirSync(outputDirectory, { recursive: true });
        }
    }
    /**
     * Generate comprehensive PDF report
     */
    async generatePDFReport(results) {
        try {
            logger_1.logger.info('Generating PDF report', { investigationId: results.investigationId });
            const pdfDoc = await pdf_lib_1.PDFDocument.create();
            const helveticaFont = await pdfDoc.embedFont(pdf_lib_1.StandardFonts.Helvetica);
            const helveticaBold = await pdfDoc.embedFont(pdf_lib_1.StandardFonts.HelveticaBold);
            // Cover page
            await this.addCoverPage(pdfDoc, results, helveticaBold, helveticaFont);
            // Executive summary
            await this.addExecutiveSummary(pdfDoc, results, helveticaBold, helveticaFont);
            // Investigation details
            await this.addInvestigationDetails(pdfDoc, results, helveticaBold, helveticaFont);
            // Transaction analysis
            await this.addTransactionAnalysis(pdfDoc, results, helveticaBold, helveticaFont);
            // Risk assessment
            await this.addRiskAssessment(pdfDoc, results, helveticaBold, helveticaFont);
            // Evidence package
            await this.addEvidencePackage(pdfDoc, results, helveticaBold, helveticaFont);
            // Appendices
            await this.addAppendices(pdfDoc, results, helveticaBold, helveticaFont);
            // Save PDF
            const pdfBytes = await pdfDoc.save();
            const filename = path_1.default.join(this.outputDirectory, `investigation_report_${results.investigationId}_${(0, date_fns_1.format)(new Date(), 'yyyyMMdd_HHmmss')}.pdf`);
            fs_1.default.writeFileSync(filename, pdfBytes);
            logger_1.logger.info('PDF report generated', { filename });
            return filename;
        }
        catch (error) {
            logger_1.logger.error('Error generating PDF report', { error: error.message });
            throw new Error(`Failed to generate PDF report: ${error.message}`);
        }
    }
    /**
     * Generate interactive HTML report
     */
    async generateHTMLReport(results) {
        try {
            logger_1.logger.info('Generating HTML report', { investigationId: results.investigationId });
            const html = this.generateHTMLTemplate(results);
            const filename = path_1.default.join(this.outputDirectory, `investigation_report_${results.investigationId}_${(0, date_fns_1.format)(new Date(), 'yyyyMMdd_HHmmss')}.html`);
            fs_1.default.writeFileSync(filename, html);
            logger_1.logger.info('HTML report generated', { filename });
            return filename;
        }
        catch (error) {
            logger_1.logger.error('Error generating HTML report', { error: error.message });
            throw new Error(`Failed to generate HTML report: ${error.message}`);
        }
    }
    /**
     * Generate JSON evidence package
     */
    async generateJSONEvidence(results) {
        try {
            logger_1.logger.info('Generating JSON evidence package', { investigationId: results.investigationId });
            const evidencePackage = {
                metadata: {
                    investigationId: results.investigationId,
                    generatedAt: new Date().toISOString(),
                    toolVersion: '3.0.0',
                    format: 'json',
                },
                investigation: results,
                integrity: {
                    totalTransactions: results.detailedTransactions.length,
                    totalEvidence: results.evidencePackage.length,
                    checksum: this.calculateChecksum(results),
                },
            };
            const filename = path_1.default.join(this.outputDirectory, `evidence_package_${results.investigationId}.json`);
            fs_1.default.writeFileSync(filename, JSON.stringify(evidencePackage, null, 2));
            logger_1.logger.info('JSON evidence package generated', { filename });
            return filename;
        }
        catch (error) {
            logger_1.logger.error('Error generating JSON evidence', { error: error.message });
            throw new Error(`Failed to generate JSON evidence: ${error.message}`);
        }
    }
    /**
     * Generate CSV data export
     */
    async generateCSVExport(results) {
        try {
            logger_1.logger.info('Generating CSV export', { investigationId: results.investigationId });
            const csvLines = [
                'Transaction ID,From Address,To Address,Amount (BTC),Depth,Timestamp,Block Height,Confirmations,Fees (BTC)',
            ];
            results.detailedTransactions.forEach(tx => {
                csvLines.push([
                    tx.txid,
                    tx.fromAddress,
                    tx.toAddress,
                    tx.amountBtc.toFixed(8),
                    tx.depth.toString(),
                    tx.timestamp,
                    tx.blockHeight?.toString() || '',
                    tx.confirmations.toString(),
                    tx.fees?.toFixed(8) || '',
                ].join(','));
            });
            const csvContent = csvLines.join('\n');
            const filename = path_1.default.join(this.outputDirectory, `transaction_data_${results.investigationId}.csv`);
            fs_1.default.writeFileSync(filename, csvContent);
            logger_1.logger.info('CSV export generated', { filename });
            return filename;
        }
        catch (error) {
            logger_1.logger.error('Error generating CSV export', { error: error.message });
            throw new Error(`Failed to generate CSV export: ${error.message}`);
        }
    }
    /**
     * Generate text report
     */
    async generateTextReport(results) {
        try {
            logger_1.logger.info('Generating text report', { investigationId: results.investigationId });
            const lines = [
                '='.repeat(80),
                '                    BITCOIN FORENSIC INVESTIGATION REPORT',
                `                        Generated: ${(0, date_fns_1.format)(new Date(), 'yyyy-MM-dd HH:mm:ss')}`,
                `                     Investigation ID: ${results.investigationId}`,
                '='.repeat(80),
                '',
                '📋 CASE INFORMATION',
                '-'.repeat(40),
                `Victim Name: ${results.inputParameters.victimName || 'N/A'}`,
                `Case Description: ${results.inputParameters.caseDescription || 'N/A'}`,
                `Investigation Date: ${(0, date_fns_1.format)(new Date(results.timestamp), 'yyyy-MM-dd HH:mm:ss')}`,
                '',
                '🔍 INVESTIGATION PARAMETERS',
                '-'.repeat(40),
                `Initial Transaction ID: ${results.inputParameters.initialTxid}`,
                `Target Address: ${results.inputParameters.targetAddress}`,
                `Maximum Depth: ${results.inputParameters.maxDepth}`,
                '',
                '📊 INVESTIGATION SUMMARY',
                '-'.repeat(40),
                `Total Transactions Found: ${results.basicResults.transactionCount}`,
                `Total Addresses Discovered: ${results.basicResults.addressCount}`,
                `Total Amount Traced: ${results.basicResults.totalAmount.toFixed(8)} BTC`,
                `Risk Level: ${results.investigationSummary.keyMetrics.riskLevel}`,
                '',
            ];
            if (results.detailedTransactions.length > 0) {
                lines.push('📋 DETAILED TRANSACTION TRAIL');
                lines.push('-'.repeat(40));
                results.detailedTransactions.forEach((tx, index) => {
                    lines.push(`Transaction #${index + 1} (Depth: ${tx.depth})`);
                    lines.push(`   ➡️  From: ${tx.fromAddress}`);
                    lines.push(`   🎯  To:   ${tx.toAddress}`);
                    lines.push(`   💰  Amount: ${tx.amountBtc.toFixed(8)} BTC`);
                    lines.push(`   🔗  TXID: ${tx.txid}`);
                    if (tx.blockHeight) {
                        lines.push(`   📦  Block: ${tx.blockHeight}`);
                    }
                    lines.push(`   ✅  Confirmed: ${tx.confirmations}`);
                    lines.push(`   🕐  Timestamp: ${tx.timestamp}`);
                    lines.push('');
                });
            }
            // Risk assessment
            if (results.advancedAnalysis.riskAssessment) {
                lines.push('🚨 RISK ASSESSMENT');
                lines.push('-'.repeat(40));
                lines.push(`Risk Level: ${results.advancedAnalysis.riskAssessment.finalRiskLevel}`);
                lines.push(`Risk Score: ${results.advancedAnalysis.riskAssessment.compositeRiskScore}/20`);
                if (results.advancedAnalysis.riskAssessment.recommendations.length > 0) {
                    lines.push('');
                    lines.push('💡 RECOMMENDATIONS:');
                    results.advancedAnalysis.riskAssessment.recommendations.forEach(rec => {
                        lines.push(`   • ${rec}`);
                    });
                }
                lines.push('');
            }
            lines.push('='.repeat(80));
            lines.push('                           END OF REPORT');
            lines.push('='.repeat(80));
            const textContent = lines.join('\n');
            const filename = path_1.default.join(this.outputDirectory, `investigation_report_${results.investigationId}_${(0, date_fns_1.format)(new Date(), 'yyyyMMdd_HHmmss')}.txt`);
            fs_1.default.writeFileSync(filename, textContent);
            logger_1.logger.info('Text report generated', { filename });
            return filename;
        }
        catch (error) {
            logger_1.logger.error('Error generating text report', { error: error.message });
            throw new Error(`Failed to generate text report: ${error.message}`);
        }
    }
    async addCoverPage(pdfDoc, results, boldFont, regularFont) {
        const page = pdfDoc.addPage([612, 792]); // Letter size
        const { width, height } = page.getSize();
        // Title
        page.drawText('BITCOIN FORENSIC INVESTIGATION REPORT', {
            x: 50,
            y: height - 100,
            size: 20,
            font: boldFont,
            color: (0, pdf_lib_1.rgb)(0, 0, 0),
        });
        // Investigation ID
        page.drawText(`Investigation ID: ${results.investigationId}`, {
            x: 50,
            y: height - 140,
            size: 12,
            font: regularFont,
            color: (0, pdf_lib_1.rgb)(0, 0, 0),
        });
        // Date
        page.drawText(`Generated: ${(0, date_fns_1.format)(new Date(), 'yyyy-MM-dd HH:mm:ss')}`, {
            x: 50,
            y: height - 160,
            size: 12,
            font: regularFont,
            color: (0, pdf_lib_1.rgb)(0, 0, 0),
        });
        // Victim information
        if (results.inputParameters.victimName) {
            page.drawText(`Victim: ${results.inputParameters.victimName}`, {
                x: 50,
                y: height - 200,
                size: 12,
                font: regularFont,
                color: (0, pdf_lib_1.rgb)(0, 0, 0),
            });
        }
        // Warning notice
        page.drawText('CONFIDENTIAL - FOR AUTHORIZED INVESTIGATION PURPOSES ONLY', {
            x: 50,
            y: 50,
            size: 10,
            font: boldFont,
            color: (0, pdf_lib_1.rgb)(0.8, 0, 0),
        });
    }
    async addExecutiveSummary(pdfDoc, results, boldFont, regularFont) {
        const page = pdfDoc.addPage([612, 792]);
        const { width, height } = page.getSize();
        let yPosition = height - 50;
        // Title
        page.drawText('EXECUTIVE SUMMARY', {
            x: 50,
            y: yPosition,
            size: 16,
            font: boldFont,
            color: (0, pdf_lib_1.rgb)(0, 0, 0),
        });
        yPosition -= 40;
        // Key metrics
        const metrics = [
            `Transactions Traced: ${results.basicResults.transactionCount}`,
            `Addresses Discovered: ${results.basicResults.addressCount}`,
            `Total Amount: ${results.basicResults.totalAmount.toFixed(8)} BTC`,
            `Risk Level: ${results.investigationSummary.keyMetrics.riskLevel}`,
        ];
        metrics.forEach(metric => {
            page.drawText(metric, {
                x: 50,
                y: yPosition,
                size: 12,
                font: regularFont,
                color: (0, pdf_lib_1.rgb)(0, 0, 0),
            });
            yPosition -= 20;
        });
    }
    async addInvestigationDetails(pdfDoc, results, boldFont, regularFont) {
        // Implementation for investigation details section
        // This would include parameters, methodology, etc.
    }
    async addTransactionAnalysis(pdfDoc, results, boldFont, regularFont) {
        // Implementation for transaction analysis section
        // This would include detailed transaction breakdown
    }
    async addRiskAssessment(pdfDoc, results, boldFont, regularFont) {
        // Implementation for risk assessment section
    }
    async addEvidencePackage(pdfDoc, results, boldFont, regularFont) {
        // Implementation for evidence package section
    }
    async addAppendices(pdfDoc, results, boldFont, regularFont) {
        // Implementation for appendices
    }
    generateHTMLTemplate(results) {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Forensic Investigation Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .chart { text-align: center; margin: 20px 0; }
        .chart img { max-width: 100%; height: auto; }
        .transaction { border: 1px solid #ddd; padding: 10px; margin: 5px 0; }
        .risk-high { color: #d32f2f; }
        .risk-medium { color: #f57c00; }
        .risk-low { color: #388e3c; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Bitcoin Forensic Investigation Report</h1>
        <p><strong>Investigation ID:</strong> ${results.investigationId}</p>
        <p><strong>Generated:</strong> ${(0, date_fns_1.format)(new Date(), 'yyyy-MM-dd HH:mm:ss')}</p>
        ${results.inputParameters.victimName ? `<p><strong>Victim:</strong> ${results.inputParameters.victimName}</p>` : ''}
    </div>

    <div class="section">
        <h2>Executive Summary</h2>
        <ul>
            <li>Transactions Traced: ${results.basicResults.transactionCount}</li>
            <li>Addresses Discovered: ${results.basicResults.addressCount}</li>
            <li>Total Amount: ${results.basicResults.totalAmount.toFixed(8)} BTC</li>
            <li>Risk Level: <span class="risk-${results.investigationSummary.keyMetrics.riskLevel.toLowerCase()}">${results.investigationSummary.keyMetrics.riskLevel}</span></li>
        </ul>
    </div>

    <div class="section">
        <h2>Transaction Analysis</h2>
        <p>Visual charts will be available in future versions. For now, please refer to the detailed transaction list below.</p>
    </div>

    <div class="section">
        <h2>Detailed Transactions</h2>
        ${results.detailedTransactions
            .map((tx, index) => `
            <div class="transaction">
                <h4>Transaction #${index + 1} (Depth: ${tx.depth})</h4>
                <p><strong>From:</strong> ${tx.fromAddress}</p>
                <p><strong>To:</strong> ${tx.toAddress}</p>
                <p><strong>Amount:</strong> ${tx.amountBtc.toFixed(8)} BTC</p>
                <p><strong>TXID:</strong> ${tx.txid}</p>
                <p><strong>Timestamp:</strong> ${tx.timestamp}</p>
            </div>
        `)
            .join('')}
    </div>

    <div class="section">
        <h2>Legal Notice</h2>
        <p><em>This report is generated for legitimate investigation purposes only.
        All information should be verified independently and used in accordance with applicable laws.</em></p>
    </div>
</body>
</html>`;
    }
    calculateChecksum(results) {
        const crypto = require('crypto');
        const data = JSON.stringify(results, null, 0);
        return crypto.createHash('sha256').update(data).digest('hex');
    }
}
exports.ReportingService = ReportingService;
//# sourceMappingURL=reporting.js.map