import { InvestigationConfig, APIResponse, BlockstreamTransaction, BlockstreamOutspend } from '../types';
export declare class BitcoinAPIService {
    private client;
    private config;
    private requestCount;
    private lastRequestTime;
    private cache;
    private batchQueue;
    private batchTimer;
    private readonly CACHE_TTL;
    private readonly BATCH_SIZE;
    private readonly BATCH_DELAY;
    constructor(config: InvestigationConfig);
    private setupInterceptors;
    private enforceRateLimit;
    /**
     * Cache management methods for performance optimization
     */
    private getCacheKey;
    private getFromCache;
    private setCache;
    private clearExpiredCache;
    /**
     * Batch processing for improved performance
     */
    private addToBatch;
    private processBatch;
    private processBatchGroup;
    getTransaction(txid: string): Promise<APIResponse<BlockstreamTransaction>>;
    private getTransactionDirect;
    getOutspend(txid: string, vout: number): Promise<APIResponse<BlockstreamOutspend>>;
    private getOutspendDirect;
    getAddressInfo(address: string): Promise<APIResponse<any>>;
    getAddressTransactions(address: string, lastSeenTxid?: string): Promise<APIResponse<any[]>>;
    private retryRequest;
    getRequestCount(): number;
    resetRequestCount(): void;
    /**
     * Performance monitoring and cleanup methods
     */
    getCacheStats(): {
        size: number;
        hitRate: number;
        memoryUsage: number;
    };
    clearCache(): void;
    cleanup(): Promise<void>;
    private getAddressInfoDirect;
}
//# sourceMappingURL=api.d.ts.map