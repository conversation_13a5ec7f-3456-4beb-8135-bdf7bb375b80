"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProgressIndicator = void 0;
const ora_1 = __importDefault(require("ora"));
const cli_progress_1 = __importDefault(require("cli-progress"));
const chalk_1 = __importDefault(require("chalk"));
class ProgressIndicator {
    /**
     * Start a spinner with a message
     */
    startSpinner(message) {
        this.stopSpinner();
        this.spinner = (0, ora_1.default)({
            text: message,
            color: 'blue',
            spinner: 'dots',
        }).start();
    }
    /**
     * Update spinner message
     */
    updateSpinner(message) {
        if (this.spinner) {
            this.spinner.text = message;
        }
    }
    /**
     * Stop spinner with success message
     */
    succeedSpinner(message) {
        if (this.spinner) {
            this.spinner.succeed(message);
            this.spinner = undefined;
        }
    }
    /**
     * Stop spinner with failure message
     */
    failSpinner(message) {
        if (this.spinner) {
            this.spinner.fail(message);
            this.spinner = undefined;
        }
    }
    /**
     * Stop spinner with warning message
     */
    warnSpinner(message) {
        if (this.spinner) {
            this.spinner.warn(message);
            this.spinner = undefined;
        }
    }
    /**
     * Stop spinner without message
     */
    stopSpinner() {
        if (this.spinner) {
            this.spinner.stop();
            this.spinner = undefined;
        }
    }
    /**
     * Start a progress bar
     */
    startProgressBar(total, message = 'Progress') {
        this.stopProgressBar();
        this.progressBar = new cli_progress_1.default.SingleBar({
            format: `${chalk_1.default.blue(message)} |${chalk_1.default.cyan('{bar}')}| {percentage}% | {value}/{total} | ETA: {eta}s`,
            barCompleteChar: '█',
            barIncompleteChar: '░',
            hideCursor: true,
        });
        this.progressBar.start(total, 0);
    }
    /**
     * Update progress bar
     */
    updateProgressBar(current, payload) {
        if (this.progressBar) {
            this.progressBar.update(current, payload);
        }
    }
    /**
     * Stop progress bar
     */
    stopProgressBar() {
        if (this.progressBar) {
            this.progressBar.stop();
            this.progressBar = undefined;
        }
    }
    /**
     * Display investigation phases
     */
    displayPhase(phase, description) {
        console.log(chalk_1.default.blue.bold(`\n🔍 ${phase.toUpperCase()}`));
        console.log(chalk_1.default.gray(`${description}\n`));
    }
    /**
     * Display step completion
     */
    displayStepComplete(step, details) {
        console.log(chalk_1.default.green(`✅ ${step}`));
        if (details) {
            console.log(chalk_1.default.gray(`   ${details}`));
        }
    }
    /**
     * Display step warning
     */
    displayStepWarning(step, details) {
        console.log(chalk_1.default.yellow(`⚠️  ${step}`));
        if (details) {
            console.log(chalk_1.default.gray(`   ${details}`));
        }
    }
    /**
     * Display step error
     */
    displayStepError(step, details) {
        console.log(chalk_1.default.red(`❌ ${step}`));
        if (details) {
            console.log(chalk_1.default.gray(`   ${details}`));
        }
    }
    /**
     * Display investigation statistics
     */
    displayStats(stats) {
        console.log(chalk_1.default.blue('\n📊 INVESTIGATION STATISTICS'));
        console.log(chalk_1.default.gray('─'.repeat(50)));
        console.log(chalk_1.default.white(`Transactions Found: ${chalk_1.default.cyan(stats.transactionsFound)}`));
        console.log(chalk_1.default.white(`Addresses Discovered: ${chalk_1.default.cyan(stats.addressesDiscovered)}`));
        console.log(chalk_1.default.white(`Total Amount Traced: ${chalk_1.default.cyan(stats.totalAmount.toFixed(8))} BTC`));
        console.log(chalk_1.default.white(`Maximum Depth: ${chalk_1.default.cyan(stats.depth)} hops`));
        console.log(chalk_1.default.white(`Investigation Time: ${chalk_1.default.cyan(Math.round(stats.duration))} seconds`));
        console.log(chalk_1.default.gray('─'.repeat(50)));
    }
    /**
     * Display risk assessment
     */
    displayRiskAssessment(riskLevel, riskScore, concerns) {
        console.log(chalk_1.default.blue('\n🚨 RISK ASSESSMENT'));
        console.log(chalk_1.default.gray('─'.repeat(50)));
        const riskColor = this.getRiskColor(riskLevel);
        console.log(chalk_1.default.white(`Risk Level: ${riskColor(riskLevel)}`));
        console.log(chalk_1.default.white(`Risk Score: ${chalk_1.default.cyan(riskScore)}/20`));
        if (concerns.length > 0) {
            console.log(chalk_1.default.white('\nKey Concerns:'));
            concerns.forEach(concern => {
                console.log(chalk_1.default.yellow(`  • ${concern.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}`));
            });
        }
        console.log(chalk_1.default.gray('─'.repeat(50)));
    }
    /**
     * Display next steps
     */
    displayNextSteps(steps) {
        if (steps.length === 0)
            return;
        console.log(chalk_1.default.blue('\n🎯 RECOMMENDED NEXT STEPS'));
        console.log(chalk_1.default.gray('─'.repeat(50)));
        steps.forEach((step, index) => {
            console.log(chalk_1.default.white(`${index + 1}. ${step}`));
        });
        console.log(chalk_1.default.gray('─'.repeat(50)));
    }
    /**
     * Display file generation progress
     */
    displayFileGeneration(files) {
        console.log(chalk_1.default.blue('\n📁 GENERATING REPORTS'));
        console.log(chalk_1.default.gray('─'.repeat(50)));
        files.forEach(file => {
            console.log(chalk_1.default.green(`✅ Generated: ${file}`));
        });
        console.log(chalk_1.default.gray('─'.repeat(50)));
    }
    /**
     * Display investigation summary
     */
    displayInvestigationSummary(summary) {
        console.log(chalk_1.default.blue.bold('\n🎉 INVESTIGATION COMPLETE!'));
        console.log(chalk_1.default.gray('═'.repeat(80)));
        console.log(chalk_1.default.white(`Investigation ID: ${chalk_1.default.cyan(summary.investigationId)}`));
        console.log(chalk_1.default.white(`Victim: ${chalk_1.default.cyan(summary.victimName)}`));
        console.log(chalk_1.default.white(`Amount Traced: ${chalk_1.default.cyan(summary.totalAmount.toFixed(8))} BTC`));
        console.log(chalk_1.default.white(`Risk Level: ${this.getRiskColor(summary.riskLevel)(summary.riskLevel)}`));
        console.log(chalk_1.default.white('\nGenerated Files:'));
        summary.filesGenerated.forEach(file => {
            console.log(chalk_1.default.gray(`  📄 ${file}`));
        });
        console.log(chalk_1.default.white(`\nAll files saved to: ${chalk_1.default.cyan(summary.outputDirectory)}`));
        console.log(chalk_1.default.gray('═'.repeat(80)));
        console.log(chalk_1.default.green.bold('Your investigation is complete! Please keep all files as evidence.'));
        console.log(chalk_1.default.yellow('Remember to report this scam to law enforcement authorities.'));
    }
    /**
     * Display API rate limiting message
     */
    displayRateLimit(waitTime) {
        console.log(chalk_1.default.yellow(`\n⏳ API rate limit reached. Waiting ${waitTime} seconds...`));
    }
    /**
     * Display error with recovery suggestions
     */
    displayErrorWithSuggestions(error, suggestions) {
        console.log(chalk_1.default.red(`\n❌ Error: ${error}`));
        if (suggestions.length > 0) {
            console.log(chalk_1.default.yellow('\n💡 Suggestions:'));
            suggestions.forEach(suggestion => {
                console.log(chalk_1.default.gray(`  • ${suggestion}`));
            });
        }
    }
    /**
     * Get color for risk level
     */
    getRiskColor(riskLevel) {
        switch (riskLevel.toUpperCase()) {
            case 'CRITICAL':
                return chalk_1.default.red.bold;
            case 'HIGH':
                return chalk_1.default.red;
            case 'MEDIUM':
                return chalk_1.default.yellow;
            case 'LOW':
                return chalk_1.default.blue;
            case 'MINIMAL':
                return chalk_1.default.green;
            default:
                return chalk_1.default.gray;
        }
    }
    /**
     * Clear console
     */
    clear() {
        console.clear();
    }
    /**
     * Add spacing
     */
    addSpacing(lines = 1) {
        console.log('\n'.repeat(lines - 1));
    }
    /**
     * Display separator
     */
    displaySeparator(char = '─', length = 50) {
        console.log(chalk_1.default.gray(char.repeat(length)));
    }
}
exports.ProgressIndicator = ProgressIndicator;
//# sourceMappingURL=progress.js.map