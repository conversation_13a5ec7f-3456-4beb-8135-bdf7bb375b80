{"version": 3, "file": "progress.d.ts", "sourceRoot": "", "sources": ["../../src/cli/progress.ts"], "names": [], "mappings": "AAIA,qBAAa,iBAAiB;IAC5B,OAAO,CAAC,OAAO,CAAC,CAAM;IACtB,OAAO,CAAC,WAAW,CAAC,CAAwB;IAE5C;;OAEG;IACH,YAAY,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IASnC;;OAEG;IACH,aAAa,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAMpC;;OAEG;IACH,cAAc,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI;IAOtC;;OAEG;IACH,WAAW,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI;IAOnC;;OAEG;IACH,WAAW,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI;IAOnC;;OAEG;IACH,WAAW,IAAI,IAAI;IAOnB;;OAEG;IACH,gBAAgB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,GAAE,MAAmB,GAAG,IAAI;IAWnE;;OAEG;IACH,iBAAiB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG,IAAI;IAMvD;;OAEG;IACH,eAAe,IAAI,IAAI;IAOvB;;OAEG;IACH,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,GAAG,IAAI;IAKtD;;OAEG;IACH,mBAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI;IAOzD;;OAEG;IACH,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI;IAOxD;;OAEG;IACH,gBAAgB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI;IAOtD;;OAEG;IACH,YAAY,CAAC,KAAK,EAAE;QAClB,iBAAiB,EAAE,MAAM,CAAC;QAC1B,mBAAmB,EAAE,MAAM,CAAC;QAC5B,WAAW,EAAE,MAAM,CAAC;QACpB,KAAK,EAAE,MAAM,CAAC;QACd,QAAQ,EAAE,MAAM,CAAC;KAClB,GAAG,IAAI;IAWR;;OAEG;IACH,qBAAqB,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,IAAI;IAkBrF;;OAEG;IACH,gBAAgB,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI;IAavC;;OAEG;IACH,qBAAqB,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI;IAW5C;;OAEG;IACH,2BAA2B,CAAC,OAAO,EAAE;QACnC,eAAe,EAAE,MAAM,CAAC;QACxB,UAAU,EAAE,MAAM,CAAC;QACnB,WAAW,EAAE,MAAM,CAAC;QACpB,SAAS,EAAE,MAAM,CAAC;QAClB,cAAc,EAAE,MAAM,EAAE,CAAC;QACzB,eAAe,EAAE,MAAM,CAAC;KACzB,GAAG,IAAI;IAqBR;;OAEG;IACH,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAIxC;;OAEG;IACH,2BAA2B,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,IAAI;IAWvE;;OAEG;IACH,OAAO,CAAC,YAAY;IAiBpB;;OAEG;IACH,KAAK,IAAI,IAAI;IAIb;;OAEG;IACH,UAAU,CAAC,KAAK,GAAE,MAAU,GAAG,IAAI;IAInC;;OAEG;IACH,gBAAgB,CAAC,IAAI,GAAE,MAAY,EAAE,MAAM,GAAE,MAAW,GAAG,IAAI;CAGhE"}