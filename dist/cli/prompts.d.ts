import { UserInput } from '../types';
export declare class CLIPrompts {
    getVictimInformation(): Promise<Partial<UserInput>>;
    getTransactionDetails(): Promise<Partial<UserInput>>;
    getInvestigationSettings(): Promise<Partial<UserInput>>;
    confirmInvestigation(userInput: UserInput): Promise<boolean>;
    askForHelp(): Promise<string>;
    askToContinue(message?: string): Promise<boolean>;
    selectOutputFormat(): Promise<string[]>;
    displayWelcome(): void;
    displayHelp(topic: string): void;
    displayError(error: string): void;
    displaySuccess(message: string): void;
    displayWarning(message: string): void;
    displayInfo(message: string): void;
}
//# sourceMappingURL=prompts.d.ts.map