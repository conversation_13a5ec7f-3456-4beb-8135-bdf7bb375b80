export declare class ProgressIndicator {
    private spinner?;
    private progressBar?;
    /**
     * Start a spinner with a message
     */
    startSpinner(message: string): void;
    /**
     * Update spinner message
     */
    updateSpinner(message: string): void;
    /**
     * Stop spinner with success message
     */
    succeedSpinner(message?: string): void;
    /**
     * Stop spinner with failure message
     */
    failSpinner(message?: string): void;
    /**
     * Stop spinner with warning message
     */
    warnSpinner(message?: string): void;
    /**
     * Stop spinner without message
     */
    stopSpinner(): void;
    /**
     * Start a progress bar
     */
    startProgressBar(total: number, message?: string): void;
    /**
     * Update progress bar
     */
    updateProgressBar(current: number, payload?: any): void;
    /**
     * Stop progress bar
     */
    stopProgressBar(): void;
    /**
     * Display investigation phases
     */
    displayPhase(phase: string, description: string): void;
    /**
     * Display step completion
     */
    displayStepComplete(step: string, details?: string): void;
    /**
     * Display step warning
     */
    displayStepWarning(step: string, details?: string): void;
    /**
     * Display step error
     */
    displayStepError(step: string, details?: string): void;
    /**
     * Display investigation statistics
     */
    displayStats(stats: {
        transactionsFound: number;
        addressesDiscovered: number;
        totalAmount: number;
        depth: number;
        duration: number;
    }): void;
    /**
     * Display risk assessment
     */
    displayRiskAssessment(riskLevel: string, riskScore: number, concerns: string[]): void;
    /**
     * Display next steps
     */
    displayNextSteps(steps: string[]): void;
    /**
     * Display file generation progress
     */
    displayFileGeneration(files: string[]): void;
    /**
     * Display investigation summary
     */
    displayInvestigationSummary(summary: {
        investigationId: string;
        victimName: string;
        totalAmount: number;
        riskLevel: string;
        filesGenerated: string[];
        outputDirectory: string;
    }): void;
    /**
     * Display API rate limiting message
     */
    displayRateLimit(waitTime: number): void;
    /**
     * Display error with recovery suggestions
     */
    displayErrorWithSuggestions(error: string, suggestions: string[]): void;
    /**
     * Get color for risk level
     */
    private getRiskColor;
    /**
     * Clear console
     */
    clear(): void;
    /**
     * Add spacing
     */
    addSpacing(lines?: number): void;
    /**
     * Display separator
     */
    displaySeparator(char?: string, length?: number): void;
}
//# sourceMappingURL=progress.d.ts.map