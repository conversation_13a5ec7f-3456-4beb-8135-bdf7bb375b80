export declare class CLIInterface {
    private prompts;
    private progress;
    private investigator?;
    private analysisService;
    private reportingService?;
    constructor();
    /**
     * Initialize and run the CLI application
     */
    run(): Promise<void>;
    /**
     * Run interactive mode
     */
    private runInteractiveMode;
    /**
     * Run quick mode with provided parameters
     */
    private runQuickMode;
    /**
     * Collect user input through prompts
     */
    private collectUserInput;
    /**
     * Run the actual investigation
     */
    private runInvestigation;
    /**
     * Show help menu
     */
    private showHelpMenu;
    /**
     * Show comprehensive help
     */
    private showComprehensiveHelp;
    /**
     * Get error suggestions based on error message
     */
    private getErrorSuggestions;
}
//# sourceMappingURL=interface.d.ts.map