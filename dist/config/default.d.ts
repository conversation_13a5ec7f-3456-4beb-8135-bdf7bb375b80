import { InvestigationConfig } from '../types';
export declare const DEFAULT_CONFIG: InvestigationConfig;
export declare const RISK_THRESHOLDS: {
    MINIMAL: number;
    LOW: number;
    MEDIUM: number;
    HIGH: number;
    CRITICAL: number;
};
export declare const SUSPICIOUS_ACTIVITY_WEIGHTS: {
    mixingServices: number;
    exchangeDeposits: number;
    peelChains: number;
    consolidationPatterns: number;
    privacyCoinInteractions: number;
};
export declare const BITCOIN_ADDRESS_PATTERNS: {
    legacy: RegExp;
    segwit: RegExp;
    taproot: RegExp;
};
export declare const TRANSACTION_ID_PATTERN: RegExp;
export declare const OUTPUT_FORMATS: {
    readonly JSON: "json";
    readonly PDF: "pdf";
    readonly HTML: "html";
    readonly CSV: "csv";
    readonly TXT: "txt";
};
export declare const EVIDENCE_TYPES: {
    readonly TRANSACTION: "transaction";
    readonly ADDRESS: "address";
    readonly PATTERN: "pattern";
    readonly ANALYSIS: "analysis";
    readonly SUMMARY: "summary";
};
export declare const LOG_LEVELS: {
    readonly ERROR: "error";
    readonly WARN: "warn";
    readonly INFO: "info";
    readonly DEBUG: "debug";
};
//# sourceMappingURL=default.d.ts.map