import { InvestigationConfig } from '../types';
import { RISK_THRESHOLDS, SUSPICIOUS_ACTIVITY_WEIGHTS, BITCOIN_ADDRESS_PATTERNS, TRANSACTION_ID_PATTERN } from '../utils/constants';
export declare const DEFAULT_CONFIG: InvestigationConfig;
export { RISK_THRESHOLDS, SUSPICIOUS_ACTIVITY_WEIGHTS, BITCOIN_ADDRESS_PATTERNS, TRANSACTION_ID_PATTERN, };
export declare const OUTPUT_FORMATS: {
    readonly JSON: "json";
    readonly PDF: "pdf";
    readonly HTML: "html";
    readonly CSV: "csv";
    readonly TXT: "txt";
};
export declare const EVIDENCE_TYPES: {
    readonly TRANSACTION: "transaction";
    readonly ADDRESS: "address";
    readonly PATTERN: "pattern";
    readonly ANALYSIS: "analysis";
    readonly SUMMARY: "summary";
};
export declare const LOG_LEVELS: {
    readonly ERROR: "error";
    readonly WARN: "warn";
    readonly INFO: "info";
    readonly DEBUG: "debug";
};
//# sourceMappingURL=default.d.ts.map