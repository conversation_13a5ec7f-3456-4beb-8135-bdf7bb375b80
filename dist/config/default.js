"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LOG_LEVELS = exports.EVIDENCE_TYPES = exports.OUTPUT_FORMATS = exports.TRANSACTION_ID_PATTERN = exports.BITCOIN_ADDRESS_PATTERNS = exports.SUSPICIOUS_ACTIVITY_WEIGHTS = exports.RISK_THRESHOLDS = exports.DEFAULT_CONFIG = void 0;
exports.DEFAULT_CONFIG = {
    apiBaseUrl: 'https://blockstream.info/api/',
    rateLimitDelay: 100, // 100ms between requests
    maxRetries: 3,
    requestTimeout: 30000, // 30 seconds
    maxDepth: 5,
    saveReports: true,
    saveVisualizations: true,
    outputDirectory: 'investigation_results',
    enableAIInsights: true,
    verboseLogging: false,
};
exports.RISK_THRESHOLDS = {
    MINIMAL: 0,
    LOW: 2,
    MEDIUM: 5,
    HIGH: 8,
    CRITICAL: 12,
};
exports.SUSPICIOUS_ACTIVITY_WEIGHTS = {
    mixingServices: 3,
    exchangeDeposits: 2,
    peelChains: 2,
    consolidationPatterns: 1,
    privacyCoinInteractions: 3,
};
exports.BITCOIN_ADDRESS_PATTERNS = {
    legacy: /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/,
    segwit: /^bc1[a-z0-9]{39,59}$/,
    taproot: /^bc1p[a-z0-9]{58}$/,
};
exports.TRANSACTION_ID_PATTERN = /^[a-fA-F0-9]{64}$/;
exports.OUTPUT_FORMATS = {
    JSON: 'json',
    PDF: 'pdf',
    HTML: 'html',
    CSV: 'csv',
    TXT: 'txt',
};
exports.EVIDENCE_TYPES = {
    TRANSACTION: 'transaction',
    ADDRESS: 'address',
    PATTERN: 'pattern',
    ANALYSIS: 'analysis',
    SUMMARY: 'summary',
};
exports.LOG_LEVELS = {
    ERROR: 'error',
    WARN: 'warn',
    INFO: 'info',
    DEBUG: 'debug',
};
//# sourceMappingURL=default.js.map