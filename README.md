# Bitcoin Forensic Investigation Tool v3.0

A comprehensive cryptocurrency forensics tool designed specifically for Bitcoin scam victims. This tool provides automated transaction tracing, AI-powered analysis, and professional evidence collection capabilities in a user-friendly CLI interface.

## 🎯 Purpose

This tool helps Bitcoin scam victims:

- Trace their stolen funds through the blockchain
- Generate professional evidence packages for law enforcement
- Understand the flow of their stolen cryptocurrency
- Collect legally admissible evidence for court proceedings

## ✨ Features

### 🔍 **Automated Investigation**

- **Transaction Tracing**: Automatically follows Bitcoin transactions through multiple hops
- **Address Analysis**: Identifies and categorizes Bitcoin addresses
- **Fund Flow Mapping**: Creates visual representations of money movement

### 🤖 **AI-Powered Analysis**

- **Pattern Detection**: Identifies suspicious transaction patterns
- **Risk Assessment**: Calculates composite risk scores
- **Behavioral Analysis**: Detects mixing services, exchanges, and laundering techniques
- **Smart Insights**: Provides AI-generated insights about transaction patterns

### 📊 **Professional Reporting**

- **PDF Reports**: Court-ready documents with professional formatting
- **Interactive HTML**: Visual charts and interactive transaction flows
- **Evidence Packages**: Cryptographically signed evidence with chain of custody
- **Multiple Formats**: JSON, CSV, and text exports for different use cases

### 🛡️ **Legal Compliance**

- **Chain of Custody**: Maintains detailed audit trails
- **Evidence Integrity**: Cryptographic hashing ensures data integrity
- **Professional Standards**: Follows forensic investigation best practices

### 🆕 **Enhanced Features (v3.0)**

- **Advanced Tracking**: Wallet clustering, mixing service detection, and real-time balance monitoring
- **Enhanced Evidence Management**: Comprehensive evidence categorization with digital signatures
- **Sophisticated Risk Assessment**: Multi-factor threat analysis with compliance flagging
- **Victim-Friendly Reports**: Plain-language reports with actionable recovery guidance
- **Performance Optimizations**: Intelligent caching and batch processing for faster investigations

## 🚀 Quick Start

### Installation

```bash
# Install Node.js (version 16 or higher)
# Download from https://nodejs.org/

# Install the tool globally
npm install -g bitcoin-forensic-investigator

# Or run directly with npx
npx bitcoin-forensic-investigator
```

### Basic Usage

1. **Interactive Mode (Recommended for beginners)**:

   ```bash
   btc-forensics investigate
   ```

2. **Quick Mode (For experienced users)**:
   ```bash
   btc-forensics quick \
     --txid "your_transaction_id" \
     --address "scammer_bitcoin_address" \
     --name "Your Name" \
     --amount 0.5
   ```

## 📋 Required Information

Before starting an investigation, gather:

- **Your full name**
- **Transaction ID (TXID)** where you sent Bitcoin to the scammer
- **Bitcoin address** you sent funds to (scammer's address)
- **Amount of Bitcoin** you lost
- **Description** of what happened
- **Date of incident** (optional)
- **Your email** (optional, for contact)

## 🔍 How to Find Your Information

### Finding Your Transaction ID (TXID)

1. Open your Bitcoin wallet
2. Go to transaction history
3. Find the transaction where you sent Bitcoin to the scammer
4. Copy the transaction ID (64-character string)

### Finding the Bitcoin Address

1. In your wallet's transaction details
2. Look for "To" or "Recipient" address
3. Bitcoin addresses start with `1`, `3`, or `bc1`

### Determining the Amount

1. Check your wallet's transaction history
2. Note the amount in BTC (not USD)
3. Example: `0.05 BTC` not `$2,000`

## 📊 Investigation Process

### Phase 1: Initialization

- Validates your input parameters
- Sets up investigation tracking
- Creates unique investigation ID

### Phase 2: Transaction Tracing

- Fetches blockchain data from reliable APIs
- Follows transaction chains through multiple hops
- Identifies all connected addresses

### Phase 3: Advanced Analysis

- Analyzes transaction patterns
- Detects suspicious activities
- Calculates risk assessments
- Generates AI insights

### Phase 4: Report Generation

- Creates professional PDF reports
- Generates interactive visualizations
- Packages evidence with chain of custody
- Exports data in multiple formats

## 📄 Output Files

Your investigation generates several files:

### 📋 **PDF Report** (`investigation_report_*.pdf`)

- Professional document suitable for law enforcement
- Executive summary and detailed findings
- Charts and visualizations
- Legal disclaimers and methodology

### 🌐 **HTML Report** (`investigation_report_*.html`)

- Interactive web-based report
- Dynamic charts and graphs
- Clickable transaction details
- Responsive design for all devices

### 📦 **Evidence Package** (`evidence_package_*.json`)

- Complete investigation data
- Cryptographic integrity verification
- Chain of custody documentation
- Suitable for legal proceedings

### 📊 **CSV Export** (`transaction_data_*.csv`)

- Spreadsheet-compatible format
- All transaction details
- Easy to analyze in Excel or similar tools

### 📝 **Text Report** (`investigation_report_*.txt`)

- Simple, readable format
- No special software required
- Easy to share via email

## 🚨 Risk Levels

The tool assigns risk levels based on analysis:

- **🟢 MINIMAL**: Standard transaction patterns, low risk
- **🔵 LOW**: Some concerning patterns but likely legitimate
- **🟡 MEDIUM**: Multiple risk factors, enhanced monitoring recommended
- **🟠 HIGH**: Significant suspicious activity, investigation recommended
- **🔴 CRITICAL**: Severe risk indicators, immediate action required

## ⚖️ Legal Considerations

### Important Notes

- This tool is for **legitimate investigation purposes only**
- Always **report scams to law enforcement**
- **Keep all generated files** as evidence
- **Consult with legal professionals** when needed

### Reporting Scams

**🇺🇸 United States:**

- FBI IC3: https://ic3.gov
- FTC: https://reportfraud.ftc.gov

**🇬🇧 United Kingdom:**

- Action Fraud: https://actionfraud.police.uk

**🇨🇦 Canada:**

- CAFC: https://antifraudcentre-centreantifraude.ca

**🌍 Other Countries:**

- Contact your local police cybercrime unit

## 🛠️ Advanced Usage

### Command Line Options

```bash
# Interactive mode with verbose logging
btc-forensics investigate --verbose

# Quick investigation with custom depth
btc-forensics quick \
  --txid "abc123..." \
  --address "1A1zP1..." \
  --name "John Doe" \
  --amount 0.1 \
  --depth 7 \
  --output "./my_investigation"

# Show comprehensive help
btc-forensics help-guide
```

### Configuration Options

- `--depth`: Investigation depth (1-10 hops, default: 5)
- `--output`: Output directory (default: `investigation_results`)
- `--verbose`: Enable detailed logging
- `--case`: Case description for quick mode

### Victim-Friendly Reports

The tool now generates special reports designed specifically for scam victims:

- **Plain Language Explanations**: Technical findings explained in simple terms
- **Recovery Assessment**: Realistic evaluation of fund recovery chances
- **Action Plans**: Step-by-step guidance for immediate and long-term actions
- **Legal Guidance**: Information about reporting requirements and legal options
- **Support Resources**: Links to victim support groups and prevention tips

These reports are automatically generated alongside technical reports and include:

- Executive summary of what happened
- Clear explanation of where funds went
- Immediate actions to take
- Legal reporting requirements
- Resources for support and prevention

## 🔧 Troubleshooting

### Common Issues

**"Invalid transaction ID"**

- Verify TXID is exactly 64 characters
- Ensure it contains only letters (a-f) and numbers (0-9)

**"Invalid Bitcoin address"**

- Check the address format
- Ensure you copied the complete address

**"No transactions found"**

- Verify the TXID and address combination
- The funds might not have been moved yet
- Check if you have the correct scammer address

**"Network errors"**

- Check your internet connection
- The blockchain API might be temporarily unavailable
- Try again in a few minutes

### Getting Help

If you encounter issues:

1. Check the logs in the `logs/` directory
2. Verify your input parameters
3. Ensure you have a stable internet connection
4. Try running with `--verbose` for more details

## 🔒 Privacy and Security

- **No private keys required**: Only uses public blockchain data
- **Local processing**: All analysis happens on your computer
- **No data sharing**: Your information stays private
- **Open source**: Code is transparent and auditable

## 📖 Comprehensive Documentation

### Getting Started

- [Installation Guide](INSTALLATION.md) - Detailed setup instructions for all platforms
- [User Guide](USER_GUIDE.md) - Complete step-by-step usage instructions
- [Examples](EXAMPLES.md) - Real-world investigation examples and case studies

### Technical Documentation

- [Project Summary](PROJECT_SUMMARY.md) - Technical overview and architecture
- [Security Guide](docs/SECURITY.md) - Security features and best practices
- [Troubleshooting](docs/TROUBLESHOOTING.md) - Common issues and solutions

### Legal and Compliance

- [Evidence Standards](docs/EVIDENCE.md) - Legal admissibility and chain of custody
- [Privacy Policy](docs/PRIVACY.md) - Data handling and privacy protection

### For Professionals

- [API Documentation](docs/API.md) - Developer reference and integration guide
- [Compliance Guide](docs/COMPLIANCE.md) - Regulatory considerations and requirements

## 📞 Support

For technical support or questions:

- Check the troubleshooting section above
- Review the comprehensive help guide: `btc-forensics help-guide`
- Consult with qualified legal and technical professionals

## ⚠️ Disclaimer

This tool is provided for educational and legitimate investigation purposes only. Users are responsible for ensuring compliance with applicable laws and regulations. The developers are not responsible for any misuse of this tool or any legal consequences arising from its use.

Always consult with law enforcement and legal professionals when dealing with cryptocurrency crimes.

## 📄 License

MIT License - see LICENSE file for details.

---

**Remember**: If you've been scammed, you're not alone. Report the crime to authorities and keep all evidence safe. This tool is here to help you gather that evidence professionally.
