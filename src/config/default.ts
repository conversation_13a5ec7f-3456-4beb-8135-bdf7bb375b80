import { InvestigationConfig } from '../types';
import {
  INVESTIGATION_DEFAULTS,
  RISK_THRESHOLDS,
  SUSPICIOUS_ACTIVITY_WEIGHTS,
  BITCOIN_ADDRESS_PATTERNS,
  TRANSACTION_ID_PATTERN,
} from '../utils/constants';

export const DEFAULT_CONFIG: InvestigationConfig = {
  apiBaseUrl: INVESTIGATION_DEFAULTS.API_BASE_URL,
  rateLimitDelay: INVESTIGATION_DEFAULTS.RATE_LIMIT_DELAY,
  maxRetries: INVESTIGATION_DEFAULTS.MAX_RETRIES,
  requestTimeout: INVESTIGATION_DEFAULTS.REQUEST_TIMEOUT,
  maxDepth: INVESTIGATION_DEFAULTS.MAX_DEPTH,
  saveReports: true,
  saveVisualizations: true,
  outputDirectory: INVESTIGATION_DEFAULTS.OUTPUT_DIRECTORY,
  enableAIInsights: true,
  verboseLogging: false,
};

// Re-export constants for backward compatibility
export {
  RIS<PERSON>_THRESHOLDS,
  SUSPICIOUS_ACTIVITY_WEIGHTS,
  BITCOIN_ADDRESS_PATTERNS,
  TRANSACTION_ID_PATTERN,
};

export const OUTPUT_FORMATS = {
  JSON: 'json',
  PDF: 'pdf',
  HTML: 'html',
  CSV: 'csv',
  TXT: 'txt',
} as const;

export const EVIDENCE_TYPES = {
  TRANSACTION: 'transaction',
  ADDRESS: 'address',
  PATTERN: 'pattern',
  ANALYSIS: 'analysis',
  SUMMARY: 'summary',
} as const;

export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug',
} as const;
