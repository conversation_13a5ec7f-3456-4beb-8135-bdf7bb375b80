// Re-export shared validation utilities for backward compatibility
export {
  validateBitcoinAddress,
  getBitcoinAddressType,
  validateTransactionId,
  validateDepth,
  validateBtcAmount,
  validateEmail,
  validateDate,
  formatValidationErrors,
  sanitizeInput,
} from './shared-validation';

// Import functions for use in local functions
import {
  validateBitcoinAddress,
  validateTransactionId,
  validateDepth,
  validateBtcAmount,
  validateEmail,
  validateDate,
} from './shared-validation';

/**
 * Validates investigation configuration
 */
export function validateConfig(config: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!config.apiBaseUrl || typeof config.apiBaseUrl !== 'string') {
    errors.push('Invalid API base URL');
  }

  if (!Number.isInteger(config.maxRetries) || config.maxRetries < 1 || config.maxRetries > 10) {
    errors.push('Max retries must be between 1 and 10');
  }

  if (!Number.isInteger(config.requestTimeout) || config.requestTimeout < 1000) {
    errors.push('Request timeout must be at least 1000ms');
  }

  if (!validateDepth(config.maxDepth)) {
    errors.push('Max depth must be between 1 and 10');
  }

  if (typeof config.rateLimitDelay !== 'number' || config.rateLimitDelay < 0) {
    errors.push('Rate limit delay must be a non-negative number');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Validates user input for investigation
 */
export function validateUserInput(input: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (
    !input.victimName ||
    typeof input.victimName !== 'string' ||
    input.victimName.trim().length === 0
  ) {
    errors.push('Victim name is required');
  }

  if (!input.initialTxid || !validateTransactionId(input.initialTxid)) {
    errors.push('Valid transaction ID is required');
  }

  if (!input.targetAddress || !validateBitcoinAddress(input.targetAddress)) {
    errors.push('Valid Bitcoin address is required');
  }

  if (!validateBtcAmount(input.scamAmount)) {
    errors.push('Valid BTC amount is required');
  }

  if (!validateDepth(input.maxDepth)) {
    errors.push('Investigation depth must be between 1 and 10');
  }

  if (input.contactInfo && !validateEmail(input.contactInfo)) {
    errors.push('Valid email address required for contact info');
  }

  if (input.incidentDate && !validateDate(input.incidentDate)) {
    errors.push('Valid date required for incident date (YYYY-MM-DD format)');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}
