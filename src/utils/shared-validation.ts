/**
 * Shared validation utilities
 * Consolidates validation logic that was duplicated across multiple files
 */

import { BITCOIN_ADDRESS_PATTERNS, TRANSACTION_ID_PATTERN } from './constants';
import { ForensicsError, ErrorCode } from './error-handler';

/**
 * Validates Bitcoin address format
 */
export function validateBitcoinAddress(address: string): boolean {
  if (!address || typeof address !== 'string') {
    return false;
  }

  // Check length constraints
  if (address.length < 26 || address.length > 62) {
    return false;
  }

  // Check against known patterns
  return Object.values(BITCOIN_ADDRESS_PATTERNS).some(pattern => pattern.test(address));
}

/**
 * Determines Bitcoin address type
 */
export function getBitcoinAddressType(
  address: string
): 'legacy' | 'segwit' | 'taproot' | 'unknown' {
  if (!validateBitcoinAddress(address)) {
    return 'unknown';
  }

  // Check Taproot first (bc1p...) since it's more specific
  if (BITCOIN_ADDRESS_PATTERNS.TAPROOT.test(address)) {
    return 'taproot';
  }

  if (BITCOIN_ADDRESS_PATTERNS.LEGACY.test(address)) {
    return 'legacy';
  }

  if (BITCOIN_ADDRESS_PATTERNS.SEGWIT.test(address)) {
    return 'segwit';
  }

  return 'unknown';
}

/**
 * Validates transaction ID format
 */
export function validateTransactionId(txid: string): boolean {
  if (!txid || typeof txid !== 'string') {
    return false;
  }
  return TRANSACTION_ID_PATTERN.test(txid);
}

/**
 * Validates investigation depth
 */
export function validateDepth(depth: number): boolean {
  return Number.isInteger(depth) && depth >= 1 && depth <= 10;
}

/**
 * Validates Bitcoin amount
 */
export function validateBtcAmount(amount: number): boolean {
  return typeof amount === 'number' && amount > 0 && amount <= 21000000;
}

/**
 * Validates email format
 */
export function validateEmail(email: string): boolean {
  if (!email || typeof email !== 'string') {
    return false;
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validates date format (YYYY-MM-DD)
 */
export function validateDate(dateString: string): boolean {
  if (!dateString || typeof dateString !== 'string') {
    return false;
  }
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(dateString)) {
    return false;
  }
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime());
}

/**
 * Validates numeric input with range constraints
 */
export function validateNumericInput(
  value: number,
  fieldName: string,
  min?: number,
  max?: number
): void {
  if (typeof value !== 'number' || isNaN(value)) {
    throw new ForensicsError(
      ErrorCode.INVALID_INPUT_PARAMETERS,
      `${fieldName} must be a valid number`
    );
  }

  if (min !== undefined && value < min) {
    throw new ForensicsError(
      ErrorCode.INVALID_INPUT_PARAMETERS,
      `${fieldName} must be at least ${min}`
    );
  }

  if (max !== undefined && value > max) {
    throw new ForensicsError(
      ErrorCode.INVALID_INPUT_PARAMETERS,
      `${fieldName} must be at most ${max}`
    );
  }
}

/**
 * Validates string input with length constraints
 */
export function validateStringInput(
  value: string,
  fieldName: string,
  minLength?: number,
  maxLength?: number,
  pattern?: RegExp
): void {
  if (!value || typeof value !== 'string') {
    throw new ForensicsError(
      ErrorCode.INVALID_INPUT_PARAMETERS,
      `${fieldName} is required and must be a string`
    );
  }

  if (minLength !== undefined && value.length < minLength) {
    throw new ForensicsError(
      ErrorCode.INVALID_INPUT_PARAMETERS,
      `${fieldName} must be at least ${minLength} characters long`
    );
  }

  if (maxLength !== undefined && value.length > maxLength) {
    throw new ForensicsError(
      ErrorCode.INVALID_INPUT_PARAMETERS,
      `${fieldName} must be at most ${maxLength} characters long`
    );
  }

  if (pattern && !pattern.test(value)) {
    throw new ForensicsError(ErrorCode.INVALID_INPUT_PARAMETERS, `${fieldName} format is invalid`);
  }
}

/**
 * Sanitizes input string to prevent XSS and injection attacks
 */
export function sanitizeInput(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/['"]/g, '') // Remove quotes
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .replace(/script/gi, '') // Remove script tags
    .substring(0, 1000); // Limit length
}

/**
 * Validates file path to prevent directory traversal
 */
export function validateFilePath(filePath: string): boolean {
  if (!filePath || typeof filePath !== 'string') {
    return false;
  }

  // Check for directory traversal patterns
  const dangerousPatterns = [
    '../',
    '..\\',
    './',
    '.\\',
    '/etc/',
    '/proc/',
    '/sys/',
    'C:\\',
    'c:\\',
  ];

  return !dangerousPatterns.some(pattern => filePath.toLowerCase().includes(pattern.toLowerCase()));
}

/**
 * Validates URL format and ensures HTTPS
 */
export function validateSecureUrl(url: string): boolean {
  if (!url || typeof url !== 'string') {
    return false;
  }

  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'https:';
  } catch {
    return false;
  }
}

/**
 * Formats validation errors for display
 */
export function formatValidationErrors(errors: string[]): string {
  if (errors.length === 0) {
    return '';
  }

  if (errors.length === 1) {
    return `❌ ${errors[0]}`;
  }

  return `❌ Multiple validation errors:\n${errors.map(error => `   • ${error}`).join('\n')}`;
}

/**
 * Creates a validation error with consistent formatting
 */
export function createValidationError(message: string, details?: any): ForensicsError {
  return new ForensicsError(ErrorCode.INVALID_INPUT_PARAMETERS, message, details);
}
