import fs from 'fs';
import path from 'path';
import { InvestigationResults, TransactionInfo, AddressInfo } from '../types';
import { logger } from '../utils/logger';

export interface VisualizationConfig {
  outputDirectory: string;
  includeInteractive: boolean;
  includeStatic: boolean;
  theme: 'light' | 'dark' | 'auto';
  maxNodes: number;
  maxEdges: number;
}

export interface NetworkNode {
  id: string;
  label: string;
  type: 'address' | 'transaction' | 'exchange' | 'mixer' | 'unknown';
  value: number; // Bitcoin amount
  level: number; // Depth in investigation
  riskScore?: number;
  metadata: {
    address?: string;
    txid?: string;
    timestamp?: string;
    confirmations?: number;
    suspicious?: boolean;
  };
}

export interface NetworkEdge {
  from: string;
  to: string;
  value: number; // Bitcoin amount
  label: string;
  type: 'transaction' | 'consolidation' | 'split';
  metadata: {
    txid: string;
    timestamp: string;
    fees?: number;
    confirmations: boolean;
  };
}

export interface VisualizationData {
  nodes: NetworkNode[];
  edges: NetworkEdge[];
  timeline: TimelineEvent[];
  riskDistribution: RiskDistribution[];
  flowSummary: FlowSummary;
}

export interface TimelineEvent {
  timestamp: string;
  type: 'transaction' | 'consolidation' | 'split' | 'exchange_deposit' | 'suspicious_activity';
  amount: number;
  description: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  metadata: Record<string, any>;
}

export interface RiskDistribution {
  category: string;
  count: number;
  totalAmount: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

export interface FlowSummary {
  totalAmount: number;
  totalTransactions: number;
  uniqueAddresses: number;
  suspiciousTransactions: number;
  exchangeDeposits: number;
  mixingServices: number;
  finalDestinations: string[];
}

export class VisualizationService {
  private config: VisualizationConfig;

  constructor(config: Partial<VisualizationConfig> = {}) {
    this.config = {
      outputDirectory: './reports',
      includeInteractive: true,
      includeStatic: true,
      theme: 'auto',
      maxNodes: 1000,
      maxEdges: 2000,
      ...config,
    };

    // Ensure output directory exists
    if (!fs.existsSync(this.config.outputDirectory)) {
      fs.mkdirSync(this.config.outputDirectory, { recursive: true });
    }
  }

  /**
   * Generate comprehensive visualizations for investigation results
   */
  async generateVisualizations(
    results: InvestigationResults,
    investigationId: string
  ): Promise<{
    interactiveReport?: string;
    networkGraph?: string;
    timeline?: string;
    riskChart?: string;
    flowDiagram?: string;
  }> {
    logger.info('Generating visualizations', { investigationId });

    const visualizationData = this.prepareVisualizationData(results);
    const generatedFiles: Record<string, string> = {};

    try {
      // Generate interactive comprehensive report
      if (this.config.includeInteractive) {
        generatedFiles.interactiveReport = await this.generateInteractiveReport(
          visualizationData,
          results,
          investigationId
        );
      }

      // Generate individual visualization components
      generatedFiles.networkGraph = await this.generateNetworkGraph(
        visualizationData,
        investigationId
      );

      generatedFiles.timeline = await this.generateTimeline(visualizationData, investigationId);

      generatedFiles.riskChart = await this.generateRiskChart(visualizationData, investigationId);

      generatedFiles.flowDiagram = await this.generateFlowDiagram(
        visualizationData,
        investigationId
      );

      logger.info('Visualizations generated successfully', {
        investigationId,
        filesGenerated: Object.keys(generatedFiles).length,
      });

      return generatedFiles;
    } catch (error) {
      logger.error('Error generating visualizations', {
        investigationId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Prepare data for visualization
   */
  private prepareVisualizationData(results: InvestigationResults): VisualizationData {
    const nodes: NetworkNode[] = [];
    const edges: NetworkEdge[] = [];
    const timeline: TimelineEvent[] = [];
    const riskDistribution: RiskDistribution[] = [];

    // Process transactions to create nodes and edges
    const addressMap = new Map<string, AddressInfo>();
    results.addressAnalysis.forEach(addr => {
      addressMap.set(addr.address, addr);
    });

    // Create address nodes
    results.addressAnalysis.forEach(addr => {
      nodes.push({
        id: addr.address,
        label: this.formatAddressLabel(addr.address),
        type: this.determineAddressType(addr),
        value: addr.totalReceived,
        level: 0, // Will be calculated based on transaction flow
        riskScore: addr.riskScore,
        metadata: {
          address: addr.address,
          suspicious: (addr.riskScore || 0) > 7,
        },
      });
    });

    // Create transaction edges and timeline events
    results.detailedTransactions.forEach(tx => {
      // Create edge for transaction
      edges.push({
        from: tx.fromAddress,
        to: tx.toAddress,
        value: tx.amountBtc,
        label: `${tx.amountBtc.toFixed(4)} BTC`,
        type: 'transaction',
        metadata: {
          txid: tx.txid,
          timestamp: tx.timestamp,
          fees: tx.fees,
          confirmations: tx.confirmations,
        },
      });

      // Create timeline event
      timeline.push({
        timestamp: tx.timestamp,
        type: 'transaction',
        amount: tx.amountBtc,
        description: `${tx.amountBtc.toFixed(4)} BTC transferred`,
        riskLevel: this.calculateTransactionRiskLevel(tx),
        metadata: {
          txid: tx.txid,
          fromAddress: tx.fromAddress,
          toAddress: tx.toAddress,
          depth: tx.depth,
        },
      });
    });

    // Calculate risk distribution
    const riskCategories = ['low', 'medium', 'high', 'critical'] as const;
    riskCategories.forEach(category => {
      const categoryTransactions = results.detailedTransactions.filter(
        tx => this.calculateTransactionRiskLevel(tx) === category
      );

      riskDistribution.push({
        category,
        count: categoryTransactions.length,
        totalAmount: categoryTransactions.reduce((sum, tx) => sum + tx.amountBtc, 0),
        riskLevel: category,
      });
    });

    // Calculate flow summary
    const flowSummary: FlowSummary = {
      totalAmount: results.basicResults.totalAmount,
      totalTransactions: results.basicResults.transactionCount,
      uniqueAddresses: results.basicResults.addressCount,
      suspiciousTransactions: results.detailedTransactions.filter(
        tx =>
          this.calculateTransactionRiskLevel(tx) === 'high' ||
          this.calculateTransactionRiskLevel(tx) === 'critical'
      ).length,
      exchangeDeposits: this.countExchangeDeposits(results.detailedTransactions),
      mixingServices: this.countMixingServices(results.detailedTransactions),
      finalDestinations: this.identifyFinalDestinations(results.detailedTransactions),
    };

    // Sort timeline by timestamp
    timeline.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

    return {
      nodes,
      edges,
      timeline,
      riskDistribution,
      flowSummary,
    };
  }

  /**
   * Generate interactive comprehensive report
   */
  private async generateInteractiveReport(
    data: VisualizationData,
    results: InvestigationResults,
    investigationId: string
  ): Promise<string> {
    const filename = path.join(
      this.config.outputDirectory,
      `interactive_investigation_${investigationId}.html`
    );

    const html = this.generateInteractiveHTML(data, results, investigationId);
    fs.writeFileSync(filename, html);

    logger.info('Interactive report generated', { filename });
    return filename;
  }

  /**
   * Generate network graph visualization
   */
  private async generateNetworkGraph(
    data: VisualizationData,
    investigationId: string
  ): Promise<string> {
    const filename = path.join(
      this.config.outputDirectory,
      `network_graph_${investigationId}.html`
    );

    const html = this.generateNetworkGraphHTML(data, investigationId);
    fs.writeFileSync(filename, html);

    logger.info('Network graph generated', { filename });
    return filename;
  }

  /**
   * Generate timeline visualization
   */
  private async generateTimeline(
    data: VisualizationData,
    investigationId: string
  ): Promise<string> {
    const filename = path.join(this.config.outputDirectory, `timeline_${investigationId}.html`);

    const html = this.generateTimelineHTML(data, investigationId);
    fs.writeFileSync(filename, html);

    logger.info('Timeline generated', { filename });
    return filename;
  }

  /**
   * Generate risk chart visualization
   */
  private async generateRiskChart(
    data: VisualizationData,
    investigationId: string
  ): Promise<string> {
    const filename = path.join(this.config.outputDirectory, `risk_chart_${investigationId}.html`);

    const html = this.generateRiskChartHTML(data, investigationId);
    fs.writeFileSync(filename, html);

    logger.info('Risk chart generated', { filename });
    return filename;
  }

  /**
   * Generate flow diagram visualization
   */
  private async generateFlowDiagram(
    data: VisualizationData,
    investigationId: string
  ): Promise<string> {
    const filename = path.join(this.config.outputDirectory, `flow_diagram_${investigationId}.html`);

    const html = this.generateFlowDiagramHTML(data, investigationId);
    fs.writeFileSync(filename, html);

    logger.info('Flow diagram generated', { filename });
    return filename;
  }

  // Helper methods
  private formatAddressLabel(address: string): string {
    return `${address.substring(0, 8)}...${address.substring(address.length - 8)}`;
  }

  private determineAddressType(addr: AddressInfo): NetworkNode['type'] {
    // Check if address has exchange-like characteristics
    if (addr.address.includes('exchange') || addr.totalReceived > 100) return 'exchange';
    // Check if address has mixer-like characteristics
    if (addr.address.includes('mixer') || (addr.riskScore || 0) > 8) return 'mixer';
    // Check if address is high risk
    if ((addr.riskScore || 0) > 7) return 'unknown';
    return 'address';
  }

  private calculateTransactionRiskLevel(
    tx: TransactionInfo
  ): 'low' | 'medium' | 'high' | 'critical' {
    if (tx.amountBtc > 10) return 'critical';
    if (tx.amountBtc > 1) return 'high';
    if (tx.amountBtc > 0.1) return 'medium';
    return 'low';
  }

  private countExchangeDeposits(transactions: TransactionInfo[]): number {
    return transactions.filter(tx => tx.toAddress.includes('exchange')).length;
  }

  private countMixingServices(transactions: TransactionInfo[]): number {
    return transactions.filter(tx => tx.toAddress.includes('mixer')).length;
  }

  private identifyFinalDestinations(transactions: TransactionInfo[]): string[] {
    const lastTransactions = transactions.slice(-5);
    return [...new Set(lastTransactions.map(tx => tx.toAddress))];
  }

  /**
   * Generate comprehensive interactive HTML report
   */
  private generateInteractiveHTML(
    data: VisualizationData,
    results: InvestigationResults,
    investigationId: string
  ): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Bitcoin Investigation Report - ${investigationId}</title>
    <script src="https://cdn.jsdelivr.net/npm/vis-network@latest/dist/vis-network.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px 10px 0 0; }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .nav-tabs { display: flex; background: #f8f9fa; border-bottom: 1px solid #dee2e6; }
        .nav-tab { padding: 15px 25px; cursor: pointer; border: none; background: none; font-size: 16px; transition: all 0.3s; }
        .nav-tab.active { background: white; border-bottom: 3px solid #667eea; color: #667eea; font-weight: bold; }
        .nav-tab:hover { background: #e9ecef; }
        .tab-content { display: none; padding: 30px; }
        .tab-content.active { display: block; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .value { font-size: 2em; font-weight: bold; color: #667eea; }
        .summary-card .label { color: #666; font-size: 0.9em; }
        .visualization-container { height: 600px; border: 1px solid #dee2e6; border-radius: 8px; margin: 20px 0; }
        .risk-indicator { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; }
        .risk-low { background: #d4edda; color: #155724; }
        .risk-medium { background: #fff3cd; color: #856404; }
        .risk-high { background: #f8d7da; color: #721c24; }
        .risk-critical { background: #f5c6cb; color: #721c24; }
        .timeline-item { display: flex; align-items: center; padding: 15px; border-bottom: 1px solid #eee; }
        .timeline-time { min-width: 150px; font-weight: bold; color: #667eea; }
        .timeline-content { flex: 1; }
        .timeline-amount { font-weight: bold; color: #28a745; }
        .alert { padding: 15px; margin: 20px 0; border-radius: 8px; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Interactive Bitcoin Investigation Report</h1>
            <p>Investigation ID: ${investigationId} | Generated: ${new Date().toLocaleString()}</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('overview')">Overview</button>
            <button class="nav-tab" onclick="showTab('network')">Network Graph</button>
            <button class="nav-tab" onclick="showTab('timeline')">Timeline</button>
            <button class="nav-tab" onclick="showTab('risk')">Risk Analysis</button>
            <button class="nav-tab" onclick="showTab('flow')">Fund Flow</button>
        </div>

        <div id="overview" class="tab-content active">
            <h2>Investigation Overview</h2>
            <div class="summary-grid">
                <div class="summary-card">
                    <h3>Total Amount</h3>
                    <div class="value">${data.flowSummary.totalAmount.toFixed(4)}</div>
                    <div class="label">Bitcoin (BTC)</div>
                </div>
                <div class="summary-card">
                    <h3>Transactions</h3>
                    <div class="value">${data.flowSummary.totalTransactions}</div>
                    <div class="label">Total traced</div>
                </div>
                <div class="summary-card">
                    <h3>Addresses</h3>
                    <div class="value">${data.flowSummary.uniqueAddresses}</div>
                    <div class="label">Unique addresses</div>
                </div>
                <div class="summary-card">
                    <h3>Risk Level</h3>
                    <div class="value">${results.advancedAnalysis.riskAssessment.finalRiskLevel}</div>
                    <div class="label">Overall assessment</div>
                </div>
            </div>

            ${
              data.flowSummary.suspiciousTransactions > 0
                ? `
            <div class="alert alert-warning">
                <strong>⚠️ Suspicious Activity Detected:</strong>
                ${data.flowSummary.suspiciousTransactions} suspicious transactions found.
                ${data.flowSummary.mixingServices > 0 ? `Mixing services detected: ${data.flowSummary.mixingServices}` : ''}
                ${data.flowSummary.exchangeDeposits > 0 ? `Exchange deposits: ${data.flowSummary.exchangeDeposits}` : ''}
            </div>
            `
                : ''
            }

            <h3>Investigation Summary</h3>
            <p><strong>Initial Transaction:</strong> ${results.inputParameters.initialTxid}</p>
            <p><strong>Target Address:</strong> ${results.inputParameters.targetAddress}</p>
            <p><strong>Investigation Depth:</strong> ${results.inputParameters.maxDepth} levels</p>
            <p><strong>Risk Score:</strong> ${results.advancedAnalysis.riskAssessment.compositeRiskScore.toFixed(2)}/10</p>

            <h3>Key Findings</h3>
            <ul>
                <li>Traced ${data.flowSummary.totalAmount.toFixed(4)} BTC through ${data.flowSummary.totalTransactions} transactions</li>
                <li>Funds passed through ${data.flowSummary.uniqueAddresses} unique addresses</li>
                <li>${data.flowSummary.suspiciousTransactions} transactions flagged as suspicious</li>
                <li>Final destinations: ${data.flowSummary.finalDestinations.length} addresses</li>
            </ul>
        </div>

        <div id="network" class="tab-content">
            <h2>Transaction Network Graph</h2>
            <p>Interactive visualization of Bitcoin address relationships and transaction flows.</p>
            <div id="networkGraph" class="visualization-container"></div>
            <div class="alert alert-info">
                <strong>💡 How to use:</strong> Click and drag nodes to explore the network.
                Hover over nodes and edges for detailed information.
                Use mouse wheel to zoom in/out.
            </div>
        </div>

        <div id="timeline" class="tab-content">
            <h2>Transaction Timeline</h2>
            <p>Chronological view of all transactions in the investigation.</p>
            <div class="timeline">
                ${data.timeline
                  .map(
                    event => `
                    <div class="timeline-item">
                        <div class="timeline-time">${new Date(event.timestamp).toLocaleString()}</div>
                        <div class="timeline-content">
                            <div class="timeline-amount">${event.amount.toFixed(4)} BTC</div>
                            <div>${event.description}</div>
                            <span class="risk-indicator risk-${event.riskLevel}">${event.riskLevel.toUpperCase()}</span>
                        </div>
                    </div>
                `
                  )
                  .join('')}
            </div>
        </div>

        <div id="risk" class="tab-content">
            <h2>Risk Analysis</h2>
            <p>Comprehensive risk assessment of the investigation.</p>
            <div id="riskChart" class="visualization-container"></div>
            <div class="summary-grid">
                ${data.riskDistribution
                  .map(
                    risk => `
                    <div class="summary-card">
                        <h3>${risk.category.toUpperCase()} Risk</h3>
                        <div class="value">${risk.count}</div>
                        <div class="label">${risk.totalAmount.toFixed(4)} BTC</div>
                    </div>
                `
                  )
                  .join('')}
            </div>
        </div>

        <div id="flow" class="tab-content">
            <h2>Fund Flow Analysis</h2>
            <p>Detailed analysis of how funds moved through the Bitcoin network.</p>
            <div id="flowChart" class="visualization-container"></div>
            <h3>Flow Summary</h3>
            <ul>
                <li><strong>Starting Amount:</strong> ${data.flowSummary.totalAmount.toFixed(4)} BTC</li>
                <li><strong>Transaction Hops:</strong> ${data.flowSummary.totalTransactions}</li>
                <li><strong>Address Changes:</strong> ${data.flowSummary.uniqueAddresses}</li>
                <li><strong>Suspicious Activities:</strong> ${data.flowSummary.suspiciousTransactions}</li>
                <li><strong>Final Destinations:</strong> ${data.flowSummary.finalDestinations.length} addresses</li>
            </ul>
        </div>
    </div>

    <script>
        // Tab switching functionality
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            // Initialize visualizations when tabs are shown
            if (tabName === 'network') {
                initNetworkGraph();
            } else if (tabName === 'risk') {
                initRiskChart();
            } else if (tabName === 'flow') {
                initFlowChart();
            }
        }

        // Network graph initialization
        function initNetworkGraph() {
            const container = document.getElementById('networkGraph');
            const nodes = new vis.DataSet(${JSON.stringify(data.nodes)});
            const edges = new vis.DataSet(${JSON.stringify(data.edges)});

            const graphData = { nodes: nodes, edges: edges };
            const options = {
                nodes: {
                    shape: 'dot',
                    size: 16,
                    font: { size: 12 },
                    borderWidth: 2,
                    shadow: true
                },
                edges: {
                    width: 2,
                    shadow: true,
                    arrows: { to: { enabled: true, scaleFactor: 1 } }
                },
                physics: {
                    enabled: true,
                    stabilization: { iterations: 100 }
                },
                interaction: {
                    hover: true,
                    tooltipDelay: 200
                }
            };

            new vis.Network(container, graphData, options);
        }

        // Risk chart initialization
        function initRiskChart() {
            const ctx = document.getElementById('riskChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ${JSON.stringify(data.riskDistribution.map(r => r.category.toUpperCase()))},
                    datasets: [{
                        data: ${JSON.stringify(data.riskDistribution.map(r => r.count))},
                        backgroundColor: ['#28a745', '#ffc107', '#fd7e14', '#dc3545']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'bottom' },
                        title: { display: true, text: 'Risk Distribution by Transaction Count' }
                    }
                }
            });
        }

        // Flow chart initialization
        function initFlowChart() {
            const ctx = document.getElementById('flowChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ${JSON.stringify(data.timeline.map(t => new Date(t.timestamp).toLocaleDateString()))},
                    datasets: [{
                        label: 'Bitcoin Amount',
                        data: ${JSON.stringify(data.timeline.map(t => t.amount))},
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: { display: true, text: 'Fund Flow Over Time' }
                    },
                    scales: {
                        y: { beginAtZero: true, title: { display: true, text: 'Bitcoin (BTC)' } },
                        x: { title: { display: true, text: 'Date' } }
                    }
                }
            });
        }

        // Initialize network graph on page load
        document.addEventListener('DOMContentLoaded', function() {
            initNetworkGraph();
        });
    </script>
</body>
</html>`;
  }

  /**
   * Generate network graph HTML
   */
  private generateNetworkGraphHTML(data: VisualizationData, investigationId: string): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Network Graph - ${investigationId}</title>
    <script src="https://cdn.jsdelivr.net/npm/vis-network@latest/dist/vis-network.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        #networkGraph { height: 80vh; border: 1px solid #ccc; }
        .controls { margin-bottom: 20px; }
        .legend { margin-top: 20px; display: flex; gap: 20px; }
        .legend-item { display: flex; align-items: center; gap: 5px; }
        .legend-color { width: 20px; height: 20px; border-radius: 50%; }
    </style>
</head>
<body>
    <h1>Bitcoin Transaction Network Graph</h1>
    <div class="controls">
        <button onclick="fitNetwork()">Fit to Screen</button>
        <button onclick="togglePhysics()">Toggle Physics</button>
        <label>
            <input type="checkbox" id="showLabels" checked onchange="toggleLabels()">
            Show Labels
        </label>
    </div>

    <div id="networkGraph"></div>

    <div class="legend">
        <div class="legend-item">
            <div class="legend-color" style="background: #97c2fc;"></div>
            <span>Regular Address</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #fb7e81;"></div>
            <span>High Risk Address</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #ffa500;"></div>
            <span>Exchange</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: #7be141;"></div>
            <span>Mixer Service</span>
        </div>
    </div>

    <script>
        let network;
        let physicsEnabled = true;

        function initNetwork() {
            const container = document.getElementById('networkGraph');

            // Prepare nodes with colors based on type and risk
            const nodes = ${JSON.stringify(data.nodes)}.map(node => ({
                ...node,
                color: getNodeColor(node),
                title: getNodeTooltip(node)
            }));

            const edges = ${JSON.stringify(data.edges)}.map(edge => ({
                ...edge,
                title: getEdgeTooltip(edge),
                color: { color: '#848484' },
                width: Math.max(1, Math.min(10, edge.value * 2))
            }));

            const data = {
                nodes: new vis.DataSet(nodes),
                edges: new vis.DataSet(edges)
            };

            const options = {
                nodes: {
                    shape: 'dot',
                    size: 20,
                    font: { size: 14, color: '#000000' },
                    borderWidth: 2,
                    shadow: true
                },
                edges: {
                    shadow: true,
                    arrows: { to: { enabled: true, scaleFactor: 1 } },
                    smooth: { type: 'continuous' }
                },
                physics: {
                    enabled: true,
                    stabilization: { iterations: 200 },
                    barnesHut: {
                        gravitationalConstant: -8000,
                        centralGravity: 0.3,
                        springLength: 95,
                        springConstant: 0.04,
                        damping: 0.09
                    }
                },
                interaction: {
                    hover: true,
                    tooltipDelay: 200,
                    hideEdgesOnDrag: true
                }
            };

            network = new vis.Network(container, data, options);

            // Add click event listener
            network.on('click', function(params) {
                if (params.nodes.length > 0) {
                    const nodeId = params.nodes[0];
                    const node = nodes.find(n => n.id === nodeId);
                    if (node) {
                        alert('Address: ' + node.metadata.address + '\\nType: ' + node.type + '\\nValue: ' + node.value + ' BTC');
                    }
                }
            });
        }

        function getNodeColor(node) {
            if (node.type === 'exchange') return '#ffa500';
            if (node.type === 'mixer') return '#7be141';
            if (node.riskScore && node.riskScore > 7) return '#fb7e81';
            return '#97c2fc';
        }

        function getNodeTooltip(node) {
            return 'Address: ' + node.label + '\\nType: ' + node.type + '\\nValue: ' + node.value.toFixed(4) + ' BTC' +
                   (node.riskScore ? '\\nRisk Score: ' + node.riskScore : '');
        }

        function getEdgeTooltip(edge) {
            return 'Transaction: ' + edge.metadata.txid.substring(0, 16) + '...\\nAmount: ' + edge.value.toFixed(4) + ' BTC\\nTime: ' + new Date(edge.metadata.timestamp).toLocaleString();
        }

        function fitNetwork() {
            network.fit();
        }

        function togglePhysics() {
            physicsEnabled = !physicsEnabled;
            network.setOptions({ physics: { enabled: physicsEnabled } });
        }

        function toggleLabels() {
            const showLabels = document.getElementById('showLabels').checked;
            network.setOptions({
                nodes: {
                    font: { size: showLabels ? 14 : 0 }
                }
            });
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', initNetwork);
    </script>
</body>
</html>`;
  }

  private generateTimelineHTML(data: VisualizationData, investigationId: string): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Investigation Timeline - ${investigationId}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 10px; padding: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .timeline { position: relative; padding: 20px 0; }
        .timeline::before { content: ''; position: absolute; left: 50px; top: 0; bottom: 0; width: 2px; background: #667eea; }
        .timeline-item { position: relative; margin: 20px 0; padding: 20px 20px 20px 80px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #667eea; }
        .timeline-item::before { content: ''; position: absolute; left: -82px; top: 25px; width: 12px; height: 12px; border-radius: 50%; background: #667eea; border: 3px solid white; }
        .timeline-time { font-weight: bold; color: #667eea; margin-bottom: 5px; }
        .timeline-amount { font-size: 1.2em; font-weight: bold; color: #28a745; margin-bottom: 5px; }
        .timeline-description { color: #333; margin-bottom: 10px; }
        .timeline-details { font-size: 0.9em; color: #666; }
        .risk-indicator { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; margin-top: 5px; }
        .risk-low { background: #d4edda; color: #155724; }
        .risk-medium { background: #fff3cd; color: #856404; }
        .risk-high { background: #f8d7da; color: #721c24; }
        .risk-critical { background: #f5c6cb; color: #721c24; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-value { font-size: 2em; font-weight: bold; color: #667eea; }
        .stat-label { color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕒 Bitcoin Investigation Timeline</h1>
        <p>Investigation ID: ${investigationId} | Generated: ${new Date().toLocaleString()}</p>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-value">${data.timeline.length}</div>
                <div class="stat-label">Total Events</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${data.flowSummary.totalAmount.toFixed(4)}</div>
                <div class="stat-label">Total BTC</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${data.timeline.filter(t => t.riskLevel === 'high' || t.riskLevel === 'critical').length}</div>
                <div class="stat-label">High Risk Events</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${Math.round((new Date(data.timeline[data.timeline.length - 1]?.timestamp || Date.now()).getTime() - new Date(data.timeline[0]?.timestamp || Date.now()).getTime()) / (1000 * 60 * 60 * 24))}</div>
                <div class="stat-label">Days Span</div>
            </div>
        </div>

        <div class="timeline">
            ${data.timeline
              .map(
                event => `
                <div class="timeline-item">
                    <div class="timeline-time">${new Date(event.timestamp).toLocaleString()}</div>
                    <div class="timeline-amount">${event.amount.toFixed(4)} BTC</div>
                    <div class="timeline-description">${event.description}</div>
                    <div class="timeline-details">
                        Type: ${event.type.replace('_', ' ').toUpperCase()}
                        ${event.metadata.txid ? `| TXID: ${event.metadata.txid.substring(0, 16)}...` : ''}
                    </div>
                    <span class="risk-indicator risk-${event.riskLevel}">${event.riskLevel.toUpperCase()} RISK</span>
                </div>
            `
              )
              .join('')}
        </div>
    </div>
</body>
</html>`;
  }

  private generateRiskChartHTML(data: VisualizationData, investigationId: string): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Investigation Risk Analysis - ${investigationId}</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 10px; padding: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .chart-container { height: 400px; margin: 30px 0; }
        .risk-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0; }
        .risk-card { padding: 20px; border-radius: 8px; text-align: center; }
        .risk-card.low { background: #d4edda; border: 2px solid #28a745; }
        .risk-card.medium { background: #fff3cd; border: 2px solid #ffc107; }
        .risk-card.high { background: #f8d7da; border: 2px solid #dc3545; }
        .risk-card.critical { background: #f5c6cb; border: 2px solid #dc3545; }
        .risk-value { font-size: 2.5em; font-weight: bold; margin-bottom: 10px; }
        .risk-label { font-size: 1.1em; font-weight: bold; margin-bottom: 5px; }
        .risk-amount { font-size: 0.9em; color: #666; }
        .summary { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚠️ Risk Analysis Dashboard</h1>
        <p>Investigation ID: ${investigationId} | Generated: ${new Date().toLocaleString()}</p>

        <div class="summary">
            <h2>Risk Assessment Summary</h2>
            <p>This analysis categorizes transactions by risk level based on amount, patterns, and suspicious indicators.</p>
        </div>

        <div class="risk-grid">
            ${data.riskDistribution
              .map(
                risk => `
                <div class="risk-card ${risk.riskLevel}">
                    <div class="risk-value">${risk.count}</div>
                    <div class="risk-label">${risk.category.toUpperCase()} RISK</div>
                    <div class="risk-amount">${risk.totalAmount.toFixed(4)} BTC</div>
                </div>
            `
              )
              .join('')}
        </div>

        <h2>Risk Distribution Charts</h2>

        <div class="chart-container">
            <canvas id="riskByCount"></canvas>
        </div>

        <div class="chart-container">
            <canvas id="riskByAmount"></canvas>
        </div>

        <div class="summary">
            <h3>Risk Indicators</h3>
            <ul>
                <li><strong>Low Risk:</strong> Small amounts, regular patterns, known addresses</li>
                <li><strong>Medium Risk:</strong> Moderate amounts, some unusual patterns</li>
                <li><strong>High Risk:</strong> Large amounts, suspicious patterns, unknown destinations</li>
                <li><strong>Critical Risk:</strong> Very large amounts, mixing services, high anonymity</li>
            </ul>
        </div>
    </div>

    <script>
        // Risk by count chart
        const ctx1 = document.getElementById('riskByCount').getContext('2d');
        new Chart(ctx1, {
            type: 'doughnut',
            data: {
                labels: ${JSON.stringify(data.riskDistribution.map(r => r.category.toUpperCase() + ' Risk'))},
                datasets: [{
                    data: ${JSON.stringify(data.riskDistribution.map(r => r.count))},
                    backgroundColor: ['#28a745', '#ffc107', '#fd7e14', '#dc3545'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Risk Distribution by Transaction Count', font: { size: 16 } },
                    legend: { position: 'bottom' }
                }
            }
        });

        // Risk by amount chart
        const ctx2 = document.getElementById('riskByAmount').getContext('2d');
        new Chart(ctx2, {
            type: 'bar',
            data: {
                labels: ${JSON.stringify(data.riskDistribution.map(r => r.category.toUpperCase()))},
                datasets: [{
                    label: 'Bitcoin Amount',
                    data: ${JSON.stringify(data.riskDistribution.map(r => r.totalAmount))},
                    backgroundColor: ['#28a745', '#ffc107', '#fd7e14', '#dc3545'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Risk Distribution by Bitcoin Amount', font: { size: 16 } },
                    legend: { display: false }
                },
                scales: {
                    y: { beginAtZero: true, title: { display: true, text: 'Bitcoin (BTC)' } },
                    x: { title: { display: true, text: 'Risk Level' } }
                }
            }
        });
    </script>
</body>
</html>`;
  }

  private generateFlowDiagramHTML(data: VisualizationData, investigationId: string): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Fund Flow Diagram - ${investigationId}</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; border-radius: 10px; padding: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .chart-container { height: 500px; margin: 30px 0; }
        .flow-summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; }
        .flow-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; }
        .flow-value { font-size: 2em; font-weight: bold; color: #667eea; }
        .flow-label { color: #666; font-size: 0.9em; }
        .destinations { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .destination-item { display: inline-block; background: white; padding: 10px; margin: 5px; border-radius: 5px; border: 1px solid #ddd; font-family: monospace; font-size: 0.9em; }
        .alert { padding: 15px; margin: 20px 0; border-radius: 8px; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>💰 Bitcoin Fund Flow Analysis</h1>
        <p>Investigation ID: ${investigationId} | Generated: ${new Date().toLocaleString()}</p>

        <div class="flow-summary">
            <div class="flow-card">
                <div class="flow-value">${data.flowSummary.totalAmount.toFixed(4)}</div>
                <div class="flow-label">Total Bitcoin Traced</div>
            </div>
            <div class="flow-card">
                <div class="flow-value">${data.flowSummary.totalTransactions}</div>
                <div class="flow-label">Transaction Hops</div>
            </div>
            <div class="flow-card">
                <div class="flow-value">${data.flowSummary.uniqueAddresses}</div>
                <div class="flow-label">Unique Addresses</div>
            </div>
            <div class="flow-card">
                <div class="flow-value">${data.flowSummary.suspiciousTransactions}</div>
                <div class="flow-label">Suspicious Activities</div>
            </div>
            <div class="flow-card">
                <div class="flow-value">${data.flowSummary.exchangeDeposits}</div>
                <div class="flow-label">Exchange Deposits</div>
            </div>
            <div class="flow-card">
                <div class="flow-value">${data.flowSummary.mixingServices}</div>
                <div class="flow-label">Mixing Services</div>
            </div>
        </div>

        ${
          data.flowSummary.suspiciousTransactions > 0
            ? `
        <div class="alert alert-warning">
            <strong>⚠️ Suspicious Activity Detected:</strong>
            ${data.flowSummary.suspiciousTransactions} suspicious transactions found in the fund flow.
            ${data.flowSummary.mixingServices > 0 ? ` ${data.flowSummary.mixingServices} mixing service(s) detected.` : ''}
            ${data.flowSummary.exchangeDeposits > 0 ? ` ${data.flowSummary.exchangeDeposits} exchange deposit(s) identified.` : ''}
        </div>
        `
            : ''
        }

        <h2>Fund Flow Over Time</h2>
        <div class="chart-container">
            <canvas id="flowChart"></canvas>
        </div>

        <h2>Transaction Volume Distribution</h2>
        <div class="chart-container">
            <canvas id="volumeChart"></canvas>
        </div>

        <div class="destinations">
            <h3>Final Destinations (${data.flowSummary.finalDestinations.length} addresses)</h3>
            <p>These are the addresses where funds were last observed:</p>
            ${data.flowSummary.finalDestinations
              .map(
                addr => `
                <div class="destination-item">${addr.substring(0, 8)}...${addr.substring(addr.length - 8)}</div>
            `
              )
              .join('')}
        </div>

        <div class="alert alert-info">
            <strong>💡 Understanding Fund Flow:</strong>
            <ul>
                <li><strong>Transaction Hops:</strong> Number of times funds moved between addresses</li>
                <li><strong>Suspicious Activities:</strong> Transactions flagged for unusual patterns</li>
                <li><strong>Exchange Deposits:</strong> Funds sent to known cryptocurrency exchanges</li>
                <li><strong>Mixing Services:</strong> Use of privacy-enhancing services to obscure fund trails</li>
                <li><strong>Final Destinations:</strong> Last known addresses in the investigation trail</li>
            </ul>
        </div>
    </div>

    <script>
        // Fund flow over time chart
        const ctx1 = document.getElementById('flowChart').getContext('2d');
        new Chart(ctx1, {
            type: 'line',
            data: {
                labels: ${JSON.stringify(data.timeline.map(t => new Date(t.timestamp).toLocaleDateString()))},
                datasets: [{
                    label: 'Bitcoin Amount',
                    data: ${JSON.stringify(data.timeline.map(t => t.amount))},
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Bitcoin Fund Flow Timeline', font: { size: 16 } },
                    legend: { display: false }
                },
                scales: {
                    y: { beginAtZero: true, title: { display: true, text: 'Bitcoin (BTC)' } },
                    x: { title: { display: true, text: 'Date' } }
                }
            }
        });

        // Transaction volume distribution
        const ctx2 = document.getElementById('volumeChart').getContext('2d');
        const volumeRanges = ['< 0.1 BTC', '0.1 - 1 BTC', '1 - 10 BTC', '> 10 BTC'];
        const volumeData = [
            ${JSON.stringify(data.timeline.filter(t => t.amount < 0.1).length)},
            ${JSON.stringify(data.timeline.filter(t => t.amount >= 0.1 && t.amount < 1).length)},
            ${JSON.stringify(data.timeline.filter(t => t.amount >= 1 && t.amount < 10).length)},
            ${JSON.stringify(data.timeline.filter(t => t.amount >= 10).length)}
        ];

        new Chart(ctx2, {
            type: 'bar',
            data: {
                labels: volumeRanges,
                datasets: [{
                    label: 'Number of Transactions',
                    data: volumeData,
                    backgroundColor: ['#28a745', '#ffc107', '#fd7e14', '#dc3545'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: { display: true, text: 'Transaction Volume Distribution', font: { size: 16 } },
                    legend: { display: false }
                },
                scales: {
                    y: { beginAtZero: true, title: { display: true, text: 'Number of Transactions' } },
                    x: { title: { display: true, text: 'Transaction Size' } }
                }
            }
        });
    </script>
</body>
</html>`;
  }
}
