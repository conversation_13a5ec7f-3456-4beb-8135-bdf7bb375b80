import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { logger, logAPICall, logError } from '../utils/logger';
import {
  InvestigationConfig,
  APIResponse,
  BlockstreamTransaction,
  BlockstreamOutspend,
} from '../types';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

interface BatchRequest {
  id: string;
  type: 'transaction' | 'outspend' | 'address';
  params: any;
  resolve: (value: any) => void;
  reject: (error: any) => void;
}

export class BitcoinAPIService {
  private client: AxiosInstance;
  private config: InvestigationConfig;
  private requestCount: number = 0;
  private lastRequestTime: number = 0;

  // Performance optimizations
  private cache = new Map<string, CacheEntry<any>>();
  private batchQueue: BatchRequest[] = [];
  private batchTimer: NodeJS.Timeout | null = null;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly BATCH_SIZE = 10;
  private readonly BATCH_DELAY = 100; // 100ms

  constructor(config: InvestigationConfig) {
    this.config = config;
    this.client = axios.create({
      baseURL: config.apiBaseUrl,
      timeout: config.requestTimeout,
      headers: {
        'User-Agent': 'Bitcoin-Forensics-Tool/3.0.0',
        Accept: 'application/json',
        Connection: 'keep-alive',
      },
      // Connection pooling for better performance
      maxRedirects: 3,
      maxContentLength: 50 * 1024 * 1024, // 50MB
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor for rate limiting
    this.client.interceptors.request.use(async config => {
      await this.enforceRateLimit();
      this.requestCount++;
      return config;
    });

    // Response interceptor for logging and error handling
    this.client.interceptors.response.use(
      response => {
        logAPICall(response.config.url || 'unknown', true, Date.now() - this.lastRequestTime);
        return response;
      },
      error => {
        const duration = Date.now() - this.lastRequestTime;
        logAPICall(error.config?.url || 'unknown', false, duration);

        if (error.response?.status === 429) {
          logger.warn('Rate limit exceeded', {
            url: error.config?.url,
            retryAfter: error.response.headers['retry-after'],
          });
        }

        return Promise.reject(error);
      }
    );
  }

  private async enforceRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.config.rateLimitDelay) {
      const waitTime = this.config.rateLimitDelay - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * Cache management methods for performance optimization
   */
  private getCacheKey(type: string, params: any): string {
    return `${type}:${JSON.stringify(params)}`;
  }

  private getFromCache<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  private setCache<T>(key: string, data: T): void {
    const now = Date.now();
    this.cache.set(key, {
      data,
      timestamp: now,
      expiresAt: now + this.CACHE_TTL,
    });
  }

  private clearExpiredCache(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Batch processing for improved performance
   */
  private addToBatch<T>(type: 'transaction' | 'outspend' | 'address', params: any): Promise<T> {
    return new Promise((resolve, reject) => {
      const id = `${type}_${Date.now()}_${Math.random()}`;
      this.batchQueue.push({ id, type, params, resolve, reject });

      if (this.batchQueue.length >= this.BATCH_SIZE) {
        this.processBatch();
      } else if (!this.batchTimer) {
        this.batchTimer = setTimeout(() => this.processBatch(), this.BATCH_DELAY);
      }
    });
  }

  private async processBatch(): Promise<void> {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    const batch = this.batchQueue.splice(0, this.BATCH_SIZE);
    if (batch.length === 0) return;

    // Group by type for efficient processing
    const groups = batch.reduce(
      (acc, req) => {
        if (!acc[req.type]) acc[req.type] = [];
        acc[req.type].push(req);
        return acc;
      },
      {} as Record<string, BatchRequest[]>
    );

    // Process each group
    for (const [type, requests] of Object.entries(groups)) {
      await this.processBatchGroup(type as any, requests);
    }
  }

  private async processBatchGroup(
    type: 'transaction' | 'outspend' | 'address',
    requests: BatchRequest[]
  ): Promise<void> {
    for (const request of requests) {
      try {
        let result;
        switch (type) {
          case 'transaction':
            result = await this.getTransactionDirect(request.params.txid);
            break;
          case 'outspend':
            result = await this.getOutspendDirect(request.params.txid, request.params.vout);
            break;
          case 'address':
            result = await this.getAddressInfoDirect(request.params.address);
            break;
        }
        request.resolve(result);
      } catch (error) {
        request.reject(error);
      }
    }
  }

  async getTransaction(txid: string): Promise<APIResponse<BlockstreamTransaction>> {
    // Check cache first
    const cacheKey = this.getCacheKey('transaction', { txid });
    const cached = this.getFromCache<APIResponse<BlockstreamTransaction>>(cacheKey);
    if (cached) {
      logger.debug('Cache hit for transaction', { txid });
      return cached;
    }

    // Use batch processing for better performance
    try {
      const result = await this.addToBatch<APIResponse<BlockstreamTransaction>>('transaction', {
        txid,
      });
      this.setCache(cacheKey, result);
      return result;
    } catch (error) {
      // Fallback to direct call
      return this.getTransactionDirect(txid);
    }
  }

  private async getTransactionDirect(txid: string): Promise<APIResponse<BlockstreamTransaction>> {
    try {
      const response: AxiosResponse<BlockstreamTransaction> = await this.retryRequest(() =>
        this.client.get(`/tx/${txid}`)
      );

      const result = {
        success: true,
        data: response.data,
      } as APIResponse<BlockstreamTransaction>;

      // Cache the result
      const cacheKey = this.getCacheKey('transaction', { txid });
      this.setCache(cacheKey, result);

      return result;
    } catch (error: any) {
      logError(error, { txid, action: 'getTransaction' });

      if (error.response?.status === 404) {
        return {
          success: false,
          error: `Transaction not found: ${txid}`,
        };
      }

      if (error.response?.status === 429) {
        return {
          success: false,
          error: 'Rate limit exceeded',
          rateLimited: true,
          retryAfter: parseInt(error.response.headers['retry-after'] || '60'),
        };
      }

      return {
        success: false,
        error: `Failed to fetch transaction: ${error.message}`,
      };
    }
  }

  async getOutspend(txid: string, vout: number): Promise<APIResponse<BlockstreamOutspend>> {
    // Check cache first
    const cacheKey = this.getCacheKey('outspend', { txid, vout });
    const cached = this.getFromCache<APIResponse<BlockstreamOutspend>>(cacheKey);
    if (cached) {
      logger.debug('Cache hit for outspend', { txid, vout });
      return cached;
    }

    // Use batch processing for better performance
    try {
      const result = await this.addToBatch<APIResponse<BlockstreamOutspend>>('outspend', {
        txid,
        vout,
      });
      this.setCache(cacheKey, result);
      return result;
    } catch (error) {
      // Fallback to direct call
      return this.getOutspendDirect(txid, vout);
    }
  }

  private async getOutspendDirect(
    txid: string,
    vout: number
  ): Promise<APIResponse<BlockstreamOutspend>> {
    try {
      const response: AxiosResponse<BlockstreamOutspend> = await this.retryRequest(() =>
        this.client.get(`/tx/${txid}/outspend/${vout}`)
      );

      const result = {
        success: true,
        data: response.data,
      } as APIResponse<BlockstreamOutspend>;

      // Cache the result
      const cacheKey = this.getCacheKey('outspend', { txid, vout });
      this.setCache(cacheKey, result);

      return result;
    } catch (error: any) {
      logError(error, { txid, vout, action: 'getOutspend' });

      if (error.response?.status === 404) {
        return {
          success: false,
          error: `Outspend not found: ${txid}:${vout}`,
        };
      }

      return {
        success: false,
        error: `Failed to fetch outspend: ${error.message}`,
      };
    }
  }

  async getAddressInfo(address: string): Promise<APIResponse<any>> {
    try {
      const response = await this.retryRequest(() => this.client.get(`/address/${address}`));

      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      logError(error, { address, action: 'getAddressInfo' });

      return {
        success: false,
        error: `Failed to fetch address info: ${error.message}`,
      };
    }
  }

  async getAddressTransactions(
    address: string,
    lastSeenTxid?: string
  ): Promise<APIResponse<any[]>> {
    try {
      const url = lastSeenTxid
        ? `/address/${address}/txs/chain/${lastSeenTxid}`
        : `/address/${address}/txs`;

      const response = await this.retryRequest(() => this.client.get(url));

      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      logError(error, { address, lastSeenTxid, action: 'getAddressTransactions' });

      return {
        success: false,
        error: `Failed to fetch address transactions: ${error.message}`,
      };
    }
  }

  private async retryRequest<T>(
    requestFn: () => Promise<AxiosResponse<T>>
  ): Promise<AxiosResponse<T>> {
    let lastError: any;

    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        return await requestFn();
      } catch (error: any) {
        lastError = error;

        if (error.response?.status === 429) {
          // Rate limited - wait longer
          const retryAfter = parseInt(error.response.headers['retry-after'] || '60');
          const waitTime = Math.min(retryAfter * 1000, 60000); // Max 60 seconds

          logger.warn(
            `Rate limited, waiting ${waitTime}ms before retry ${attempt}/${this.config.maxRetries}`
          );
          await new Promise(resolve => setTimeout(resolve, waitTime));
          continue;
        }

        if (error.response?.status === 404) {
          // Don't retry 404s
          throw error;
        }

        if (attempt < this.config.maxRetries) {
          // Exponential backoff for other errors
          const waitTime = Math.min(1000 * Math.pow(2, attempt - 1), 10000); // Max 10 seconds
          logger.warn(
            `Request failed, retrying in ${waitTime}ms (attempt ${attempt}/${this.config.maxRetries})`
          );
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }
    }

    throw lastError;
  }

  getRequestCount(): number {
    return this.requestCount;
  }

  resetRequestCount(): void {
    this.requestCount = 0;
  }

  /**
   * Performance monitoring and cleanup methods
   */
  getCacheStats(): { size: number; hitRate: number; memoryUsage: number } {
    const size = this.cache.size;
    const memoryUsage = JSON.stringify([...this.cache.entries()]).length;

    return {
      size,
      hitRate: 0, // Would need to track hits/misses for accurate calculation
      memoryUsage,
    };
  }

  clearCache(): void {
    this.cache.clear();
    logger.info('API cache cleared');
  }

  async cleanup(): Promise<void> {
    // Clear any pending batch timer
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    // Process any remaining batch items
    if (this.batchQueue.length > 0) {
      await this.processBatch();
    }

    // Clear expired cache entries
    this.clearExpiredCache();

    logger.info('API service cleanup completed');
  }

  private async getAddressInfoDirect(address: string): Promise<APIResponse<any>> {
    try {
      const response = await this.retryRequest(() => this.client.get(`/address/${address}`));

      const result = {
        success: true,
        data: response.data,
      };

      // Cache the result
      const cacheKey = this.getCacheKey('address', { address });
      this.setCache(cacheKey, result);

      return result;
    } catch (error: any) {
      logError(error, { address, action: 'getAddressInfo' });

      return {
        success: false,
        error: `Failed to fetch address info: ${error.message}`,
      };
    }
  }
}
