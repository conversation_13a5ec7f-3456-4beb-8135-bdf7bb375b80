import { Command } from 'commander';
import chalk from 'chalk';
import path from 'path';
import { CLIPrompts } from './prompts';
import { ProgressIndicator } from './progress';
import { BitcoinForensicsInvestigator } from '../core/investigator';
import { AnalysisService } from '../services/analysis';
import { UnifiedReportingService } from '../services/unified-reporting';
import { UserInput, InvestigationResults } from '../types';
import { validateUserInput, formatValidationErrors } from '../utils/validation';
import { logger, setLogLevel } from '../utils/logger';

export class CLIInterface {
  private prompts: CLIPrompts;
  private progress: ProgressIndicator;
  private investigator?: BitcoinForensicsInvestigator;
  private analysisService: AnalysisService;
  private reportingService?: UnifiedReportingService;

  constructor() {
    this.prompts = new CLIPrompts();
    this.progress = new ProgressIndicator();
    this.analysisService = new AnalysisService();
  }

  /**
   * Initialize and run the CLI application
   */
  async run(): Promise<void> {
    const program = new Command();

    program
      .name('btc-forensics')
      .description('Bitcoin Forensic Investigation Tool for Scam Victims')
      .version('3.0.0');

    // Interactive mode (default)
    program
      .command('investigate', { isDefault: true })
      .description('Start an interactive investigation')
      .option('-v, --verbose', 'Enable verbose logging')
      .option('-o, --output <dir>', 'Output directory', 'investigation_results')
      .action(async options => {
        if (options.verbose) {
          setLogLevel('debug');
        }
        await this.runInteractiveMode(options);
      });

    // Quick mode with parameters
    program
      .command('quick')
      .description('Run investigation with provided parameters')
      .requiredOption('-t, --txid <txid>', 'Transaction ID')
      .requiredOption('-a, --address <address>', 'Target Bitcoin address')
      .requiredOption('-n, --name <name>', 'Victim name')
      .requiredOption('-m, --amount <amount>', 'Amount lost in BTC')
      .option('-d, --depth <depth>', 'Investigation depth', '5')
      .option('-c, --case <description>', 'Case description')
      .option('-v, --verbose', 'Enable verbose logging')
      .option('-o, --output <dir>', 'Output directory', 'investigation_results')
      .action(async options => {
        if (options.verbose) {
          setLogLevel('debug');
        }
        await this.runQuickMode(options);
      });

    // Help command
    program
      .command('help-guide')
      .description('Show comprehensive help guide')
      .action(() => {
        this.showComprehensiveHelp();
      });

    await program.parseAsync();
  }

  /**
   * Run interactive mode
   */
  private async runInteractiveMode(options: any): Promise<void> {
    try {
      this.prompts.displayWelcome();

      // Check if user needs help first
      const needsHelp = await this.prompts.askToContinue(
        'Do you need help finding your transaction information?'
      );

      if (needsHelp) {
        await this.showHelpMenu();
      }

      // Collect user information
      const userInput = await this.collectUserInput();

      // Validate input
      const validation = validateUserInput(userInput);
      if (!validation.valid) {
        this.prompts.displayError(formatValidationErrors(validation.errors));
        return;
      }

      // Confirm before starting
      const confirmed = await this.prompts.confirmInvestigation(userInput);
      if (!confirmed) {
        this.prompts.displayInfo('Investigation cancelled by user.');
        return;
      }

      // Initialize investigator and reporting service
      this.investigator = new BitcoinForensicsInvestigator({
        outputDirectory: options.output,
        verboseLogging: options.verbose,
      });

      this.reportingService = new UnifiedReportingService(options.output);

      // Run investigation
      await this.runInvestigation(userInput);
    } catch (error: any) {
      logger.error('CLI error in interactive mode', { error: error.message });
      this.progress.failSpinner('Investigation failed');
      this.prompts.displayError(error.message);

      const suggestions = this.getErrorSuggestions(error.message);
      if (suggestions.length > 0) {
        this.progress.displayErrorWithSuggestions(error.message, suggestions);
      }
    }
  }

  /**
   * Run quick mode with provided parameters
   */
  private async runQuickMode(options: any): Promise<void> {
    try {
      const userInput: UserInput = {
        victimName: options.name,
        caseDescription: options.case || 'Quick investigation via CLI',
        initialTxid: options.txid,
        targetAddress: options.address,
        scamAmount: parseFloat(options.amount),
        maxDepth: parseInt(options.depth),
      };

      // Validate input
      const validation = validateUserInput(userInput);
      if (!validation.valid) {
        this.prompts.displayError(formatValidationErrors(validation.errors));
        return;
      }

      console.log(chalk.blue('🚀 Starting quick investigation...\n'));
      console.log(chalk.gray(`Victim: ${userInput.victimName}`));
      console.log(chalk.gray(`TXID: ${userInput.initialTxid}`));
      console.log(chalk.gray(`Address: ${userInput.targetAddress}`));
      console.log(chalk.gray(`Amount: ${userInput.scamAmount} BTC`));
      console.log(chalk.gray(`Depth: ${userInput.maxDepth} hops\n`));

      // Initialize investigator and reporting service
      this.investigator = new BitcoinForensicsInvestigator({
        outputDirectory: options.output,
        verboseLogging: options.verbose,
      });

      this.reportingService = new UnifiedReportingService(options.output);

      // Run investigation
      await this.runInvestigation(userInput);
    } catch (error: any) {
      logger.error('CLI error in quick mode', { error: error.message });
      this.prompts.displayError(error.message);
    }
  }

  /**
   * Collect user input through prompts
   */
  private async collectUserInput(): Promise<UserInput> {
    // Get victim information
    const victimInfo = await this.prompts.getVictimInformation();

    // Get transaction details
    const transactionDetails = await this.prompts.getTransactionDetails();

    // Get investigation settings
    const investigationSettings = await this.prompts.getInvestigationSettings();

    return {
      ...victimInfo,
      ...transactionDetails,
      ...investigationSettings,
    } as UserInput;
  }

  /**
   * Run the actual investigation
   */
  private async runInvestigation(userInput: UserInput): Promise<void> {
    const startTime = Date.now();

    try {
      // Phase 1: Initialize Investigation
      this.progress.displayPhase('Phase 1', 'Initializing investigation and validating inputs');
      this.progress.startSpinner('Setting up investigation...');

      if (!this.investigator) {
        throw new Error('Investigator not initialized');
      }

      this.progress.succeedSpinner('Investigation initialized');
      this.progress.displayStepComplete(
        'Investigation ID generated',
        this.investigator.getInvestigationId()
      );

      // Phase 2: Transaction Tracing
      this.progress.displayPhase('Phase 2', 'Tracing Bitcoin transactions through the blockchain');
      this.progress.startSpinner('Fetching initial transaction data...');

      const results = await this.investigator.startInvestigation(userInput);

      this.progress.succeedSpinner('Transaction tracing completed');
      this.progress.displayStepComplete(
        'Transactions traced',
        `Found ${results.detailedTransactions.length} transactions across ${results.addressAnalysis.length} addresses`
      );

      // Phase 3: Advanced Analysis
      if (results.detailedTransactions.length > 0) {
        this.progress.displayPhase(
          'Phase 3',
          'Performing advanced pattern analysis and risk assessment'
        );
        this.progress.startSpinner('Analyzing transaction patterns...');

        // Perform advanced analysis
        const patterns = this.analysisService.analyzeTransactionPatterns(
          results.detailedTransactions
        );
        const suspiciousActivity = this.analysisService.detectSuspiciousActivity(
          results.detailedTransactions
        );
        const riskAssessment = this.analysisService.generateRiskAssessment(
          results.detailedTransactions,
          patterns,
          suspiciousActivity
        );

        // Update results with analysis
        results.advancedAnalysis = {
          transactionPatterns: patterns,
          suspiciousActivity,
          riskAssessment,
        };

        // Update investigation summary
        results.investigationSummary.keyMetrics.riskLevel = riskAssessment.finalRiskLevel;
        results.investigationSummary.keyConcerns = riskAssessment.suspiciousActivities;

        this.progress.succeedSpinner('Advanced analysis completed');
        this.progress.displayStepComplete(
          'Risk assessment',
          `Risk level: ${riskAssessment.finalRiskLevel}`
        );
      }

      // Phase 4: Report Generation
      this.progress.displayPhase(
        'Phase 4',
        'Generating comprehensive reports and evidence package'
      );
      this.progress.startSpinner('Generating reports...');

      const generatedFiles: string[] = [];

      if (this.reportingService) {
        try {
          // Generate comprehensive reports using unified service
          this.progress.updateSpinner('Generating comprehensive reports...');
          const reportResults = await this.reportingService.generateReports(
            results,
            undefined, // Enhanced risk assessment (would be available from investigation results)
            undefined, // Advanced tracking results (would be available from investigation results)
            {
              includeVictimFriendly: true,
              includeTechnical: true,
              includeVisualizations: true,
              outputFormats: ['pdf', 'html', 'text', 'json', 'csv'],
            }
          );

          // Add generated files to the list
          reportResults.allFiles.forEach(filePath => {
            generatedFiles.push(path.basename(filePath));
          });

          this.progress.updateSpinner('Reports generated successfully');
        } catch (error: any) {
          logger.error('Error generating reports', { error: error.message });
          this.progress.warnSpinner('Some reports could not be generated');
        }
      }

      this.progress.succeedSpinner('Reports generated successfully');
      this.progress.displayFileGeneration(generatedFiles);

      // Display results
      const duration = (Date.now() - startTime) / 1000;

      this.progress.displayStats({
        transactionsFound: results.detailedTransactions.length,
        addressesDiscovered: results.addressAnalysis.length,
        totalAmount: results.basicResults.totalAmount,
        depth: results.investigationSummary.keyMetrics.maximumDepth,
        duration,
      });

      if (results.advancedAnalysis.riskAssessment) {
        this.progress.displayRiskAssessment(
          results.advancedAnalysis.riskAssessment.finalRiskLevel,
          results.advancedAnalysis.riskAssessment.compositeRiskScore,
          results.advancedAnalysis.riskAssessment.suspiciousActivities
        );

        this.progress.displayNextSteps(results.advancedAnalysis.riskAssessment.recommendations);
      }

      // Final summary
      this.progress.displayInvestigationSummary({
        investigationId: this.investigator.getInvestigationId(),
        victimName: userInput.victimName,
        totalAmount: results.basicResults.totalAmount,
        riskLevel: results.investigationSummary.keyMetrics.riskLevel,
        filesGenerated: generatedFiles,
        outputDirectory: this.investigator['config'].outputDirectory,
      });
    } catch (error: any) {
      this.progress.failSpinner('Investigation failed');
      throw error;
    }
  }

  /**
   * Show help menu
   */
  private async showHelpMenu(): Promise<void> {
    let showingHelp = true;

    while (showingHelp) {
      const helpTopic = await this.prompts.askForHelp();

      if (helpTopic === 'back') {
        showingHelp = false;
      } else {
        this.prompts.displayHelp(helpTopic);
        await this.prompts.askToContinue('Press Enter to continue...');
      }
    }
  }

  /**
   * Show comprehensive help
   */
  private showComprehensiveHelp(): void {
    console.log(
      chalk.blue.bold('\n📚 BITCOIN FORENSIC INVESTIGATION TOOL - COMPREHENSIVE GUIDE\n')
    );

    console.log(chalk.white('OVERVIEW:'));
    console.log(
      chalk.gray('This tool helps Bitcoin scam victims trace their stolen funds through the')
    );
    console.log(
      chalk.gray('blockchain and generate professional evidence packages for law enforcement.\n')
    );

    console.log(chalk.white('USAGE MODES:'));
    console.log(chalk.cyan('1. Interactive Mode (Recommended for beginners):'));
    console.log(chalk.gray('   btc-forensics investigate'));
    console.log(chalk.gray('   - Guided step-by-step process'));
    console.log(chalk.gray('   - Built-in help and validation'));
    console.log(chalk.gray('   - User-friendly prompts\n'));

    console.log(chalk.cyan('2. Quick Mode (For experienced users):'));
    console.log(chalk.gray('   btc-forensics quick -t <txid> -a <address> -n <name> -m <amount>'));
    console.log(chalk.gray('   - Fast investigation with known parameters'));
    console.log(chalk.gray('   - Suitable for automation\n'));

    console.log(chalk.white('REQUIRED INFORMATION:'));
    console.log(chalk.gray('• Your full name'));
    console.log(chalk.gray('• Transaction ID (TXID) where you sent Bitcoin'));
    console.log(chalk.gray('• Bitcoin address you sent funds to'));
    console.log(chalk.gray('• Amount of Bitcoin lost'));
    console.log(chalk.gray('• Description of what happened\n'));

    console.log(chalk.white('OUTPUT FILES:'));
    console.log(chalk.gray('• PDF report for legal proceedings'));
    console.log(chalk.gray('• Interactive HTML visualization'));
    console.log(chalk.gray('• JSON evidence package'));
    console.log(chalk.gray('• Audit trail documentation\n'));

    console.log(chalk.yellow('IMPORTANT LEGAL NOTES:'));
    console.log(chalk.gray('• This tool is for legitimate investigation purposes only'));
    console.log(chalk.gray('• Always report scams to law enforcement'));
    console.log(chalk.gray('• Keep all generated files as evidence'));
    console.log(chalk.gray('• Consult with legal professionals when needed\n'));
  }

  /**
   * Get error suggestions based on error message
   */
  private getErrorSuggestions(errorMessage: string): string[] {
    const suggestions: string[] = [];

    if (errorMessage.includes('transaction') || errorMessage.includes('txid')) {
      suggestions.push('Verify your transaction ID is correct (64 characters, hex)');
      suggestions.push('Check your wallet history or blockchain explorer');
    }

    if (errorMessage.includes('address')) {
      suggestions.push('Verify the Bitcoin address format');
      suggestions.push('Ensure you copied the complete address');
    }

    if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
      suggestions.push('Check your internet connection');
      suggestions.push('Try again in a few minutes');
      suggestions.push('The blockchain API might be temporarily unavailable');
    }

    if (errorMessage.includes('rate limit')) {
      suggestions.push('Wait a few minutes before trying again');
      suggestions.push('The tool will automatically retry with delays');
    }

    return suggestions;
  }
}
