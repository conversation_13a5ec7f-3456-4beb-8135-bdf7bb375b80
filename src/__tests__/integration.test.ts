import { BitcoinForensicsInvestigator } from '../core/investigator';
import { UnifiedReportingService } from '../services/unified-reporting';
import { VisualizationService } from '../services/visualization';
import { UserInput } from '../types';
import fs from 'fs';
import path from 'path';

// Mock external dependencies
jest.mock('fs');
jest.mock('../services/api');

const mockFs = fs as jest.Mocked<typeof fs>;

describe('Integration Tests', () => {
  let investigator: BitcoinForensicsInvestigator;
  let reportingService: UnifiedReportingService;
  let visualizationService: VisualizationService;
  const testOutputDir = './test-integration-output';

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock fs operations
    mockFs.existsSync.mockReturnValue(false);
    mockFs.mkdirSync.mockImplementation(() => undefined);
    mockFs.writeFileSync.mockImplementation(() => undefined);
    mockFs.readFileSync.mockReturnValue('test file content');

    // Initialize services
    investigator = new BitcoinForensicsInvestigator({
      outputDirectory: testOutputDir,
      maxDepth: 3,
      verboseLogging: false,
    });

    reportingService = new UnifiedReportingService(testOutputDir);
    visualizationService = new VisualizationService({
      outputDirectory: testOutputDir,
    });
  });

  describe('End-to-End Investigation Flow', () => {
    const validUserInput: UserInput = {
      initialTxid: 'a1b2c3d4e5f67890123456789012345678901234567890123456789012345678',
      targetAddress: '**********************************',
      maxDepth: 3,
      victimName: 'Test Victim',
      caseDescription: 'Integration test case',
      scamAmount: 0.5,
    };

    it('should complete full investigation workflow', async () => {
      // Mock API responses
      const mockApiService = require('../services/api').BitcoinAPIService;
      mockApiService.prototype.getTransaction = jest.fn().mockResolvedValue({
        txid: validUserInput.initialTxid,
        inputs: [{ address: 'input-address', value: 50000000 }],
        outputs: [{ address: validUserInput.targetAddress, value: 50000000 }],
        blockHeight: 800000,
        timestamp: '2024-01-15T10:30:00.000Z',
        confirmations: 6,
        fees: 1000,
      });

      mockApiService.prototype.getAddressInfo = jest.fn().mockResolvedValue({
        address: validUserInput.targetAddress,
        totalReceived: 0.5,
        totalSent: 0.3,
        transactionCount: 5,
        balance: 0.2,
      });

      mockApiService.prototype.getAddressTransactions = jest.fn().mockResolvedValue([
        {
          txid: 'tx1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
          inputs: [{ address: validUserInput.targetAddress, value: 30000000 }],
          outputs: [{ address: 'output-address', value: 30000000 }],
          blockHeight: 800001,
          timestamp: '2024-01-15T11:30:00.000Z',
          confirmations: 5,
          fees: 1000,
        },
      ]);

      // Run investigation
      const results = await investigator.startInvestigation(validUserInput);

      // Verify investigation completed
      expect(results).toBeDefined();
      expect(results.investigationId).toBeDefined();
      expect(results.inputParameters).toEqual(validUserInput);
      expect(results.basicResults).toBeDefined();
      expect(results.detailedTransactions).toBeDefined();
      expect(results.addressAnalysis).toBeDefined();
    });

    it('should handle investigation errors gracefully', async () => {
      const mockApiService = require('../services/api').BitcoinAPIService;
      mockApiService.prototype.getTransaction = jest.fn().mockRejectedValue(new Error('API Error'));

      await expect(investigator.startInvestigation(validUserInput)).rejects.toThrow();
    });
  });

  describe('Reporting Integration', () => {
    const mockResults = {
      investigationId: 'test-integration-123',
      timestamp: '2024-01-15T10:30:00.000Z',
      inputParameters: {
        initialTxid: 'a1b2c3d4e5f67890123456789012345678901234567890123456789012345678',
        targetAddress: '**********************************',
        maxDepth: 3,
        victimName: 'Test Victim',
        caseDescription: 'Integration test',
      },
      basicResults: {
        transactionCount: 5,
        totalAmount: 1.0,
        addressCount: 4,
        investigationDepth: 2,
      },
      detailedTransactions: [
        {
          txid: 'tx1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
          fromAddress: '**********************************',
          toAddress: '**********************************',
          amountBtc: 0.5,
          depth: 1,
          timestamp: '2024-01-15T10:30:00.000Z',
          blockHeight: 800000,
          confirmations: true,
          fees: 0.0001,
        },
      ],
      addressAnalysis: [
        {
          address: '**********************************',
          totalReceived: 0.5,
          totalSent: 0.5,
          transactionCount: 1,
          firstSeen: '2024-01-15T10:30:00.000Z',
          lastSeen: '2024-01-15T10:30:00.000Z',
          riskScore: 3,
        },
      ],
      advancedAnalysis: {
        riskAssessment: {
          finalRiskLevel: 'LOW' as const,
          compositeRiskScore: 3.0,
          suspicionScore: 2.0,
          recommendations: ['No immediate action required'],
        },
        suspiciousActivity: {
          overallSuspicionScore: 2.0,
          mixingServices: { detected: false, confidence: 0 },
          exchangeDeposits: { detected: false, confidence: 0 },
          peelChains: { detected: false, confidence: 0 },
          consolidationPatterns: { detected: false, confidence: 0 },
        },
      },
      investigationSummary: {
        status: 'completed' as const,
        keyMetrics: {
          maximumDepth: 2,
          totalUniqueAddresses: 4,
          suspiciousTransactionCount: 0,
          riskDistribution: { low: 5, medium: 0, high: 0, critical: 0 },
        },
        findings: ['Low risk investigation'],
        recommendations: ['No further action needed'],
      },
      evidencePackage: [],
      auditTrail: [],
    };

    it('should generate comprehensive reports', async () => {
      const reportResults = await reportingService.generateReports(
        mockResults,
        undefined,
        undefined,
        {
          includeVictimFriendly: true,
          includeTechnical: true,
          includeVisualizations: true,
          outputFormats: ['html', 'text', 'json'],
        }
      );

      expect(reportResults).toBeDefined();
      expect(reportResults.allFiles).toBeDefined();
      expect(reportResults.allFiles.length).toBeGreaterThan(0);

      // Verify files were written
      expect(mockFs.writeFileSync).toHaveBeenCalled();
    });

    it('should generate visualizations', async () => {
      const visualizationResults = await visualizationService.generateVisualizations(
        mockResults,
        'test-integration-123'
      );

      expect(visualizationResults).toBeDefined();
      expect(visualizationResults.interactiveReport).toBeDefined();
      expect(visualizationResults.networkGraph).toBeDefined();
      expect(visualizationResults.timeline).toBeDefined();
      expect(visualizationResults.riskChart).toBeDefined();
      expect(visualizationResults.flowDiagram).toBeDefined();
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle file system errors', async () => {
      mockFs.writeFileSync.mockImplementation(() => {
        throw new Error('Disk full');
      });

      const mockResults = {
        investigationId: 'error-test',
        timestamp: '2024-01-15T10:30:00.000Z',
        inputParameters: {
          initialTxid: 'a1b2c3d4e5f67890123456789012345678901234567890123456789012345678',
          targetAddress: '**********************************',
          maxDepth: 1,
          victimName: 'Test',
          caseDescription: 'Test',
        },
        basicResults: {
          transactionCount: 0,
          totalAmount: 0,
          addressCount: 0,
          investigationDepth: 0,
        },
        detailedTransactions: [],
        addressAnalysis: [],
        advancedAnalysis: {
          riskAssessment: {
            finalRiskLevel: 'LOW' as const,
            compositeRiskScore: 0,
            suspicionScore: 0,
            recommendations: [],
          },
          suspiciousActivity: {
            overallSuspicionScore: 0,
            mixingServices: { detected: false, confidence: 0 },
            exchangeDeposits: { detected: false, confidence: 0 },
            peelChains: { detected: false, confidence: 0 },
            consolidationPatterns: { detected: false, confidence: 0 },
          },
        },
        investigationSummary: {
          status: 'completed' as const,
          keyMetrics: {
            maximumDepth: 0,
            totalUniqueAddresses: 0,
            suspiciousTransactionCount: 0,
            riskDistribution: { low: 0, medium: 0, high: 0, critical: 0 },
          },
          findings: [],
          recommendations: [],
        },
        evidencePackage: [],
        auditTrail: [],
      };

      await expect(reportingService.generateReports(mockResults)).rejects.toThrow();
    });

    it('should validate input parameters', () => {
      const invalidInput: UserInput = {
        initialTxid: 'invalid-txid',
        targetAddress: 'invalid-address',
        maxDepth: -1,
        victimName: '',
        caseDescription: '',
        scamAmount: -1,
      };

      expect(() => {
        // This should trigger validation errors
        investigator.startInvestigation(invalidInput);
      }).rejects.toThrow();
    });
  });

  describe('Performance Integration', () => {
    it('should handle large datasets efficiently', async () => {
      const largeResults = {
        investigationId: 'large-test',
        timestamp: '2024-01-15T10:30:00.000Z',
        inputParameters: {
          initialTxid: 'a1b2c3d4e5f67890123456789012345678901234567890123456789012345678',
          targetAddress: '**********************************',
          maxDepth: 5,
          victimName: 'Large Test',
          caseDescription: 'Performance test',
        },
        basicResults: {
          transactionCount: 1000,
          totalAmount: 100.0,
          addressCount: 500,
          investigationDepth: 5,
        },
        detailedTransactions: Array.from({ length: 1000 }, (_, i) => ({
          txid: `tx${i.toString().padStart(60, '0')}abcdef`,
          fromAddress: `1Address${i}`,
          toAddress: `1Address${i + 1}`,
          amountBtc: 0.1,
          depth: Math.floor(i / 200) + 1,
          timestamp: new Date(Date.now() + i * 1000).toISOString(),
          blockHeight: 800000 + i,
          confirmations: true,
          fees: 0.0001,
        })),
        addressAnalysis: Array.from({ length: 500 }, (_, i) => ({
          address: `1Address${i}`,
          totalReceived: 0.2,
          totalSent: 0.1,
          transactionCount: 2,
          firstSeen: new Date(Date.now() + i * 1000).toISOString(),
          lastSeen: new Date(Date.now() + i * 2000).toISOString(),
          riskScore: Math.floor(Math.random() * 10),
        })),
        advancedAnalysis: {
          riskAssessment: {
            finalRiskLevel: 'MEDIUM' as const,
            compositeRiskScore: 5.0,
            suspicionScore: 4.0,
            recommendations: ['Monitor activity'],
          },
          suspiciousActivity: {
            overallSuspicionScore: 4.0,
            mixingServices: { detected: true, confidence: 0.7 },
            exchangeDeposits: { detected: true, confidence: 0.8 },
            peelChains: { detected: false, confidence: 0 },
            consolidationPatterns: { detected: true, confidence: 0.6 },
          },
        },
        investigationSummary: {
          status: 'completed' as const,
          keyMetrics: {
            maximumDepth: 5,
            totalUniqueAddresses: 500,
            suspiciousTransactionCount: 50,
            riskDistribution: { low: 800, medium: 150, high: 40, critical: 10 },
          },
          findings: ['Large scale investigation'],
          recommendations: ['Detailed analysis required'],
        },
        evidencePackage: [],
        auditTrail: [],
      };

      const startTime = Date.now();

      await visualizationService.generateVisualizations(largeResults, 'large-test');

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (10 seconds)
      expect(duration).toBeLessThan(10000);
    });
  });

  describe('Security Integration', () => {
    it('should sanitize file paths', async () => {
      const maliciousResults = {
        investigationId: '../../../malicious',
        timestamp: '2024-01-15T10:30:00.000Z',
        inputParameters: {
          initialTxid: 'a1b2c3d4e5f67890123456789012345678901234567890123456789012345678',
          targetAddress: '**********************************',
          maxDepth: 1,
          victimName: '<script>alert("xss")</script>',
          caseDescription: 'Security test',
        },
        basicResults: {
          transactionCount: 0,
          totalAmount: 0,
          addressCount: 0,
          investigationDepth: 0,
        },
        detailedTransactions: [],
        addressAnalysis: [],
        advancedAnalysis: {
          riskAssessment: {
            finalRiskLevel: 'LOW' as const,
            compositeRiskScore: 0,
            suspicionScore: 0,
            recommendations: [],
          },
          suspiciousActivity: {
            overallSuspicionScore: 0,
            mixingServices: { detected: false, confidence: 0 },
            exchangeDeposits: { detected: false, confidence: 0 },
            peelChains: { detected: false, confidence: 0 },
            consolidationPatterns: { detected: false, confidence: 0 },
          },
        },
        investigationSummary: {
          status: 'completed' as const,
          keyMetrics: {
            maximumDepth: 0,
            totalUniqueAddresses: 0,
            suspiciousTransactionCount: 0,
            riskDistribution: { low: 0, medium: 0, high: 0, critical: 0 },
          },
          findings: [],
          recommendations: [],
        },
        evidencePackage: [],
        auditTrail: [],
      };

      // Should not throw errors and should sanitize the investigation ID
      const result = await visualizationService.generateVisualizations(
        maliciousResults,
        'sanitized-id'
      );

      expect(result).toBeDefined();

      // Verify that file paths don't contain malicious content
      const writeFileCalls = mockFs.writeFileSync.mock.calls;
      writeFileCalls.forEach(call => {
        const filePath = call[0] as string;
        expect(filePath).not.toContain('../');
        expect(filePath).not.toContain('<script>');
      });
    });
  });
});
