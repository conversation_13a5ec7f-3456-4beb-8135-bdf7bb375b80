import { VisualizationService } from '../services/visualization';
import { InvestigationResults } from '../types';
import fs from 'fs';
import path from 'path';

// Mock fs module
jest.mock('fs');
const mockFs = fs as jest.Mocked<typeof fs>;

describe('VisualizationService', () => {
  let visualizationService: VisualizationService;
  let mockResults: InvestigationResults;
  const testOutputDir = './test-output';

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock fs.existsSync to return false initially
    mockFs.existsSync.mockReturnValue(false);

    // Mock fs.mkdirSync
    mockFs.mkdirSync.mockImplementation(() => undefined);

    // Mock fs.writeFileSync
    mockFs.writeFileSync.mockImplementation(() => undefined);

    visualizationService = new VisualizationService({
      outputDirectory: testOutputDir,
      includeInteractive: true,
      includeStatic: true,
      theme: 'light',
      maxNodes: 100,
      maxEdges: 200,
    });

    // Create mock investigation results
    mockResults = {
      investigationId: 'test-investigation-123',
      timestamp: '2024-01-15T10:30:00.000Z',
      inputParameters: {
        initialTxid: 'a1b2c3d4e5f67890123456789012345678901234567890123456789012345678',
        targetAddress: '1A********************************',
        maxDepth: 5,
        victimName: 'Test Victim',
        caseDescription: 'Test case',
      },
      basicResults: {
        transactionCount: 10,
        totalAmount: 1.5,
        addressCount: 8,
      },
      detailedTransactions: [
        {
          txid: 'tx1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
          fromAddress: '1A********************************',
          toAddress: '**********************************',
          amountBtc: 0.5,
          depth: 1,
          timestamp: '2024-01-15T10:30:00.000Z',
          blockHeight: 800000,
          confirmations: true,
          investigationId: 'test-investigation-id',
          fees: 0.0001,
        },
        {
          txid: 'tx2234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
          fromAddress: '**********************************',
          toAddress: '1C********************************',
          amountBtc: 1.0,
          depth: 2,
          timestamp: '2024-01-15T11:30:00.000Z',
          blockHeight: 800001,
          confirmations: true,
          investigationId: 'test-investigation-id',
          fees: 0.0002,
        },
      ],
      addressAnalysis: [
        {
          address: '1A********************************',
          addressType: 'legacy' as const,
          totalReceived: 0.5,
          totalSent: 0.5,
          balance: 0.0,
          transactionCount: 2,
          firstSeen: '2024-01-15T10:30:00.000Z',
          lastSeen: '2024-01-15T11:30:00.000Z',
          riskScore: 3,
        },
        {
          address: '**********************************',
          addressType: 'legacy' as const,
          totalReceived: 1.0,
          totalSent: 1.0,
          balance: 0.0,
          transactionCount: 2,
          firstSeen: '2024-01-15T10:30:00.000Z',
          lastSeen: '2024-01-15T11:30:00.000Z',
          riskScore: 7,
        },
      ],
      advancedAnalysis: {
        riskAssessment: {
          finalRiskLevel: 'MEDIUM' as const,
          compositeRiskScore: 6.5,
          baseRiskScore: 4.0,
          suspicionScore: 5.0,
          riskFactors: ['high_transaction_volume'],
          suspiciousActivities: ['unusual_timing'],
          recommendations: ['Monitor for additional activity'],
        },
        suspiciousActivity: {
          overallSuspicionScore: 5.0,
          suspicionLevel: 'MEDIUM',
          mixingServices: { detected: false, severity: 0, confidence: 0, details: {} },
          exchangeDeposits: { detected: false, severity: 0, confidence: 0, details: {} },
          peelChains: { detected: false, severity: 0, confidence: 0, details: {} },
          consolidationPatterns: { detected: false, severity: 0, confidence: 0, details: {} },
          privacyCoinInteractions: { detected: false, severity: 0, confidence: 0, details: {} },
        },
        transactionPatterns: {
          timingAnalysis: {
            averageIntervalSeconds: 300,
            rapidSuccessionCount: 0,
            totalIntervals: 1,
            suspiciousTiming: false,
            timePatterns: [],
          },
          amountAnalysis: {
            totalAmount: 1.5,
            averageAmount: 0.75,
            maxAmount: 1.0,
            minAmount: 0.5,
            roundAmountsCount: 0,
            similarAmountsCount: 0,
            potentialStructuring: false,
            potentialSplitting: false,
          },
          addressReuse: {
            uniqueFromAddresses: 2,
            uniqueToAddresses: 2,
            reusedFromAddresses: {},
            reusedToAddresses: {},
            addressReuseDetected: false,
          },
          clusteringHints: {
            totalClusters: 0,
            potentialClusters: 0,
            clusterDetails: {},
            clusteringDetected: false,
          },
          riskIndicators: {
            riskScore: 5.0,
            riskLevel: 'MEDIUM',
            riskFactors: ['high_transaction_volume'],
            totalAmount: 1.5,
            transactionCount: 2,
            maxDepth: 2,
          },
        },
      },
      investigationSummary: {
        status: 'completed',
        keyMetrics: {
          totalTransactions: 2,
          totalAmountBtc: 1.5,
          uniqueAddresses: 2,
          maximumDepth: 2,
          riskLevel: 'MEDIUM',
        },
        keyConcerns: ['Medium risk investigation'],
        investigationQuality: {
          qualityScore: 85,
          qualityLevel: 'HIGH' as const,
          qualityFactors: ['complete_transaction_trace'],
          completenessPercentage: 85,
        },
        nextSteps: ['Continue monitoring'],
        timeline: [],
      },
      evidencePackage: [],
      auditTrail: [],
    };
  });

  describe('constructor', () => {
    it('should create output directory if it does not exist', () => {
      expect(mockFs.existsSync).toHaveBeenCalledWith(testOutputDir);
      expect(mockFs.mkdirSync).toHaveBeenCalledWith(testOutputDir, { recursive: true });
    });

    it('should not create directory if it already exists', () => {
      // Clear previous calls from beforeEach
      jest.clearAllMocks();
      mockFs.existsSync.mockReturnValue(true);

      new VisualizationService({ outputDirectory: testOutputDir });

      expect(mockFs.mkdirSync).not.toHaveBeenCalled();
    });
  });

  describe('generateVisualizations', () => {
    it('should generate all visualization files', async () => {
      const result = await visualizationService.generateVisualizations(
        mockResults,
        'test-investigation-123'
      );

      expect(result).toHaveProperty('interactiveReport');
      expect(result).toHaveProperty('networkGraph');
      expect(result).toHaveProperty('timeline');
      expect(result).toHaveProperty('riskChart');
      expect(result).toHaveProperty('flowDiagram');

      // Verify files were written
      expect(mockFs.writeFileSync).toHaveBeenCalledTimes(5);
    });

    it('should generate correct file names', async () => {
      const result = await visualizationService.generateVisualizations(
        mockResults,
        'test-investigation-123'
      );

      expect(result.interactiveReport).toContain(
        'interactive_investigation_test-investigation-123.html'
      );
      expect(result.networkGraph).toContain('network_graph_test-investigation-123.html');
      expect(result.timeline).toContain('timeline_test-investigation-123.html');
      expect(result.riskChart).toContain('risk_chart_test-investigation-123.html');
      expect(result.flowDiagram).toContain('flow_diagram_test-investigation-123.html');
    });

    it('should handle empty transaction data', async () => {
      const emptyResults = {
        ...mockResults,
        detailedTransactions: [],
        addressAnalysis: [],
      };

      const result = await visualizationService.generateVisualizations(emptyResults, 'empty-test');

      expect(result).toHaveProperty('interactiveReport');
      expect(mockFs.writeFileSync).toHaveBeenCalled();
    });
  });

  describe('data preparation', () => {
    it('should create correct network nodes', async () => {
      await visualizationService.generateVisualizations(mockResults, 'test');

      // Check that writeFileSync was called with HTML content containing nodes
      const writeFileCalls = mockFs.writeFileSync.mock.calls;
      const networkGraphCall = writeFileCalls.find(call =>
        call[0].toString().includes('network_graph')
      );

      expect(networkGraphCall).toBeDefined();
      expect(networkGraphCall![1]).toContain('1A********************************');
      expect(networkGraphCall![1]).toContain('**********************************');
    });

    it('should create correct network edges', async () => {
      await visualizationService.generateVisualizations(mockResults, 'test');

      const writeFileCalls = mockFs.writeFileSync.mock.calls;
      const networkGraphCall = writeFileCalls.find(call =>
        call[0].toString().includes('network_graph')
      );

      expect(networkGraphCall![1]).toContain('tx1234567890abcdef');
      expect(networkGraphCall![1]).toContain('0.5000 BTC');
    });

    it('should create timeline events', async () => {
      await visualizationService.generateVisualizations(mockResults, 'test');

      const writeFileCalls = mockFs.writeFileSync.mock.calls;
      const timelineCall = writeFileCalls.find(call => call[0].toString().includes('timeline'));

      expect(timelineCall![1]).toContain('0.5000 BTC transferred');
      expect(timelineCall![1]).toContain('1.0000 BTC transferred');
    });

    it('should calculate risk distribution correctly', async () => {
      await visualizationService.generateVisualizations(mockResults, 'test');

      const writeFileCalls = mockFs.writeFileSync.mock.calls;
      const riskCall = writeFileCalls.find(call => call[0].toString().includes('risk_chart'));

      expect(riskCall![1]).toContain('LOW Risk');
      expect(riskCall![1]).toContain('MEDIUM Risk');
    });
  });

  describe('HTML generation', () => {
    it('should generate valid HTML structure', async () => {
      await visualizationService.generateVisualizations(mockResults, 'test');

      const writeFileCalls = mockFs.writeFileSync.mock.calls;

      writeFileCalls.forEach(call => {
        const htmlContent = call[1] as string;
        expect(htmlContent).toContain('<!DOCTYPE html>');
        expect(htmlContent).toContain('<html');
        expect(htmlContent).toContain('</html>');
        expect(htmlContent).toContain('<head>');
        expect(htmlContent).toContain('<body>');
      });
    });

    it('should include required JavaScript libraries', async () => {
      await visualizationService.generateVisualizations(mockResults, 'test');

      const writeFileCalls = mockFs.writeFileSync.mock.calls;
      const interactiveCall = writeFileCalls.find(call =>
        call[0].toString().includes('interactive_investigation')
      );

      expect(interactiveCall![1]).toContain('vis-network');
      expect(interactiveCall![1]).toContain('chart.js');
    });

    it('should include investigation data in JavaScript', async () => {
      await visualizationService.generateVisualizations(mockResults, 'test');

      const writeFileCalls = mockFs.writeFileSync.mock.calls;
      const interactiveCall = writeFileCalls.find(call =>
        call[0].toString().includes('interactive_investigation')
      );

      expect(interactiveCall![1]).toContain('test');
      expect(interactiveCall![1]).toContain('1.5000');
      expect(interactiveCall![1]).toContain('MEDIUM');
    });
  });

  describe('error handling', () => {
    it('should handle file write errors gracefully', async () => {
      mockFs.writeFileSync.mockImplementation(() => {
        throw new Error('Write failed');
      });

      await expect(
        visualizationService.generateVisualizations(mockResults, 'test')
      ).rejects.toThrow('Write failed');
    });

    it('should handle invalid investigation data', async () => {
      const invalidResults = {
        ...mockResults,
        detailedTransactions: null as any,
      };

      await expect(
        visualizationService.generateVisualizations(invalidResults, 'test')
      ).rejects.toThrow();
    });
  });

  describe('configuration options', () => {
    it('should respect maxNodes configuration', () => {
      const service = new VisualizationService({
        outputDirectory: testOutputDir,
        maxNodes: 50,
      });

      expect(service).toBeDefined();
    });

    it('should respect theme configuration', () => {
      const service = new VisualizationService({
        outputDirectory: testOutputDir,
        theme: 'dark',
      });

      expect(service).toBeDefined();
    });
  });
});
