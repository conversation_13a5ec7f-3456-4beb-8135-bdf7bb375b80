{"version": 3, "file": "PDFPageLeaf.js", "sourceRoot": "", "sources": ["../../../src/core/structures/PDFPageLeaf.ts"], "names": [], "mappings": ";AAAA,OAAO,QAAQ,4BAAkC;AACjD,OAAO,OAAoB,2BAAiC;AAC5D,OAAO,OAAO,2BAAiC;AAC/C,OAAO,SAAS,6BAAmC;AAGnD,OAAO,SAAS,6BAAmC;AAInD;IAA0B,+BAAO;IA0B/B,qBACE,GAAY,EACZ,OAAmB,EACnB,gBAAuB;QAAvB,iCAAA,EAAA,uBAAuB;QAHzB,YAKE,kBAAM,GAAG,EAAE,OAAO,CAAC,SAEpB;QAVO,gBAAU,GAAG,KAAK,CAAC;QASzB,KAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;;IAC3C,CAAC;IAED,2BAAK,GAAL,UAAM,OAAoB;QACxB,IAAM,KAAK,GAAG,WAAW,CAAC,kBAAkB,CAC1C,IAAI,GAAG,EAAE,EACT,OAAO,IAAI,IAAI,CAAC,OAAO,EACvB,IAAI,CAAC,gBAAgB,CACtB,CAAC;QACF,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC/B,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;YAClD,IAAA,KAAe,OAAO,CAAC,GAAG,CAAC,EAA1B,GAAG,QAAA,EAAE,KAAK,QAAgB,CAAC;YAClC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;SACvB;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,4BAAM,GAAN;QACE,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAA4B,CAAC;IAC9E,CAAC;IAED,8BAAQ,GAAR;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,CAG5B,CAAC;IAChB,CAAC;IAED,4BAAM,GAAN;QACE,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IACpD,CAAC;IAED,8BAAQ,GAAR;QACE,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED,6BAAO,GAAP;QACE,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACrD,CAAC;IAED,4BAAM,GAAN;QACE,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IACpD,CAAC;IAED,+BAAS,GAAT;QACE,IAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED,8BAAQ,GAAR;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACnD,CAAC;IAED,6BAAO,GAAP;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACxD,CAAC;IAED,4BAAM,GAAN;QACE,IAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IAC1D,CAAC;IAED,6CAAuB,GAAvB,UAAwB,IAAa;QACnC,IAAI,SAAgC,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,UAAC,IAAI;YACf,IAAI,CAAC,SAAS;gBAAE,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QACH,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,+BAAS,GAAT,UAAU,SAAiB;QACzB,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IACtC,CAAC;IAED,sCAAgB,GAAhB,UAAiB,gBAAwB;QACvC,IAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC3E,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACrC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAClC,CAAC;IAED,wCAAkB,GAAlB,UAAmB,WAAmB,EAAE,SAAiB;QACvD,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QACjC,IAAI,QAAQ,YAAY,QAAQ,EAAE;YAChC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;YAChC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACzB,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,8BAAQ,GAAR,UAAS,QAAgB;QACf,IAAA,MAAM,GAAK,IAAI,CAAC,iBAAiB,EAAE,OAA7B,CAA8B;QAC5C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxB,CAAC;IAED,iCAAW,GAAX,UAAY,QAAgB;QAClB,IAAA,MAAM,GAAK,IAAI,CAAC,iBAAiB,EAAE,OAA7B,CAA8B;QAC5C,IAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACvC,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACtB;IACH,CAAC;IAED,uCAAiB,GAAjB,UAAkB,IAAa,EAAE,WAAmB;QAC1C,IAAA,IAAI,GAAK,IAAI,CAAC,iBAAiB,EAAE,KAA7B,CAA8B;QAC1C,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IAC9B,CAAC;IAED,0CAAoB,GAApB,UAAqB,GAAW;QACtB,IAAA,IAAI,GAAK,IAAI,CAAC,iBAAiB,EAAE,KAA7B,CAA8B;QAC1C,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;IAED,uCAAiB,GAAjB,UAAkB,GAAW,EAAE,WAAmB;QAChD,IAAM,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QACzC,OAAO,GAAG,CAAC;IACb,CAAC;IAED,gCAAU,GAAV,UAAW,IAAa,EAAE,UAAkB;QAClC,IAAA,OAAO,GAAK,IAAI,CAAC,iBAAiB,EAAE,QAA7B,CAA8B;QAC7C,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IAChC,CAAC;IAED,mCAAa,GAAb,UAAc,GAAW;QACf,IAAA,OAAO,GAAK,IAAI,CAAC,iBAAiB,EAAE,QAA7B,CAA8B;QAC7C,OAAO,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAED,gCAAU,GAAV,UAAW,GAAW,EAAE,UAAkB;QACxC,IAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;QACjC,OAAO,GAAG,CAAC;IACb,CAAC;IAED,kCAAY,GAAZ,UAAa,IAAa,EAAE,YAA8B;QAChD,IAAA,SAAS,GAAK,IAAI,CAAC,iBAAiB,EAAE,UAA7B,CAA8B;QAC/C,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;IACpC,CAAC;IAED,qCAAe,GAAf,UAAgB,GAAW;QACjB,IAAA,SAAS,GAAK,IAAI,CAAC,iBAAiB,EAAE,UAA7B,CAA8B;QAC/C,OAAO,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IAClC,CAAC;IAED,kCAAY,GAAZ,UAAa,GAAW,EAAE,YAA8B;QACtD,IAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QACtC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;QACrC,OAAO,GAAG,CAAC;IACb,CAAC;IAED,4BAAM,GAAN,UAAO,OAAiD;QACtD,OAAO,CAAC,IAAI,CAAC,CAAC;QACd,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC7B,IAAI,MAAM;YAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IAED,+BAAS,GAAT;QACE,IAAI,IAAI,CAAC,UAAU;YAAE,OAAO;QAEpB,IAAA,OAAO,GAAK,IAAI,QAAT,CAAU;QAEzB,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAClD,IAAI,QAAQ,YAAY,SAAS,EAAE;YACjC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SACxD;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,kBAAkB,CACrB,IAAI,CAAC,OAAO,CAAC,iCAAiC,EAAE,EAChD,IAAI,CAAC,OAAO,CAAC,gCAAgC,EAAE,CAChD,CAAC;SACH;QAED,6CAA6C;QAC7C,IAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAClE,IAAM,SAAS,GACb,OAAO,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC7D,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAEvC,wCAAwC;QACxC,IAAM,IAAI,GACR,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAClE,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAElC,2CAA2C;QAC3C,IAAM,OAAO,GACX,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACrE,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAExC,6CAA6C;QAC7C,IAAM,SAAS,GACb,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACvE,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAE5C,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAChD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAEjC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACzB,CAAC;IAED,uCAAiB,GAAjB;QACE,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAG,CAAC;QAC9B,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,EAAG,CAAC;QACpC,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAA0B,CAAC;QACzD,OAAO;YACL,MAAM,QAAA;YACN,SAAS,WAAA;YACT,QAAQ,UAAA;YACR,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;YAC7C,OAAO,EAAE,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC;YACnD,SAAS,EAAE,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC;SACxD,CAAC;IACJ,CAAC;IAxPe,8BAAkB,GAAG;QACnC,WAAW;QACX,UAAU;QACV,SAAS;QACT,QAAQ;KACT,CAAC;IAEK,gCAAoB,GAAG,UAAC,OAAmB,EAAE,MAAc;QAChE,IAAM,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACjC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1D,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IAC/C,CAAC,CAAC;IAEK,8BAAkB,GAAG,UAC1B,GAAY,EACZ,OAAmB,EACnB,gBAAuB;QAAvB,iCAAA,EAAA,uBAAuB;QACpB,OAAA,IAAI,WAAW,CAAC,GAAG,EAAE,OAAO,EAAE,gBAAgB,CAAC;IAA/C,CAA+C,CAAC;IAqOvD,kBAAC;CAAA,AA1PD,CAA0B,OAAO,GA0PhC;AAED,eAAe,WAAW,CAAC"}