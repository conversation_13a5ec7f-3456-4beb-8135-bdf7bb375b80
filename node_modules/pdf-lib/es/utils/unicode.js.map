{"version": 3, "file": "unicode.js", "sourceRoot": "", "sources": ["../../src/utils/unicode.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,kBAA0B;AAEhD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsFG;AACH,MAAM,CAAC,IAAM,UAAU,GAAG,UAAC,KAAa,EAAE,aAAoB;IAApB,8BAAA,EAAA,oBAAoB;IAC5D,IAAM,OAAO,GAAG,EAAE,CAAC;IAEnB,IAAI,aAAa;QAAE,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAElD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAI;QACjD,IAAM,SAAS,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,CAAE,CAAC;QAE1C,oBAAoB;QACpB,IAAI,SAAS,GAAG,IAAI,EAAE;YACpB,IAAM,KAAK,GAAG,SAAS,GAAG,IAAI,CAAC;YAC/B,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,GAAG,IAAI,CAAC,CAAC;SACV;QAED,oBAAoB;aACf,IAAI,SAAS,GAAG,MAAM,EAAE;YAC3B,IAAM,KAAK,GAAG,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YAC/C,IAAM,KAAK,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YACxC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAC3B,GAAG,IAAI,CAAC,CAAC;SACV;QAED,sBAAsB;aACjB,IAAI,SAAS,GAAG,QAAQ,EAAE;YAC7B,IAAM,KAAK,GAAG,CAAC,CAAC,SAAS,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YAChD,IAAM,KAAK,GAAG,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YAC/C,IAAM,KAAK,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YACxC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAClC,GAAG,IAAI,CAAC,CAAC;SACV;QAED,sCAAsC;aACjC,IAAI,SAAS,GAAG,QAAQ,EAAE;YAC7B,IAAM,KAAK,GAAG,CAAC,CAAC,SAAS,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YAChD,IAAM,KAAK,GAAG,CAAC,CAAC,SAAS,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YAChD,IAAM,KAAK,GAAG,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YAC/C,IAAM,KAAK,GAAG,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YACzC,GAAG,IAAI,CAAC,CAAC;SACV;QAED,+BAA+B;;YAC1B,MAAM,IAAI,KAAK,CAAC,2BAAyB,WAAW,CAAC,SAAS,CAAG,CAAC,CAAC;KACzE;IAED,OAAO,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;AACjC,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+DG;AACH,MAAM,CAAC,IAAM,WAAW,GAAG,UACzB,KAAa,EACb,aAAoB;IAApB,8BAAA,EAAA,oBAAoB;IAEpB,IAAM,OAAO,GAAG,EAAE,CAAC;IAEnB,IAAI,aAAa;QAAE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAExC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAI;QACjD,IAAM,SAAS,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,CAAE,CAAC;QAE1C,oBAAoB;QACpB,IAAI,SAAS,GAAG,QAAQ,EAAE;YACxB,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxB,GAAG,IAAI,CAAC,CAAC;SACV;QAED,sCAAsC;aACjC,IAAI,SAAS,GAAG,QAAQ,EAAE;YAC7B,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;YAChE,GAAG,IAAI,CAAC,CAAC;SACV;QAED,+BAA+B;;YAC1B,MAAM,IAAI,KAAK,CAAC,2BAAyB,WAAW,CAAC,SAAS,CAAG,CAAC,CAAC;KACzE;IAED,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;AAClC,CAAC,CAAC;AAEF;;;;;;;GAOG;AACH,MAAM,CAAC,IAAM,WAAW,GAAG,UAAC,SAAiB;IAC3C,OAAA,SAAS,IAAI,CAAC,IAAI,SAAS,IAAI,MAAM;AAArC,CAAqC,CAAC;AAExC;;;;;;GAMG;AACH,MAAM,CAAC,IAAM,aAAa,GAAG,UAAC,SAAiB;IAC7C,OAAA,SAAS,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ;AAA9C,CAA8C,CAAC;AAEjD,sCAAsC;AACtC,sDAAsD;AACtD,MAAM,CAAC,IAAM,aAAa,GAAG,UAAC,SAAiB;IAC7C,OAAA,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM;AAAlD,CAAkD,CAAC;AAErD,sCAAsC;AACtC,sDAAsD;AACtD,MAAM,CAAC,IAAM,YAAY,GAAG,UAAC,SAAiB;IAC5C,OAAA,CAAC,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM;AAAxC,CAAwC,CAAC;AAE3C,IAAK,SAGJ;AAHD,WAAK,SAAS;IACZ,oCAAuB,CAAA;IACvB,0CAA6B,CAAA;AAC/B,CAAC,EAHI,SAAS,KAAT,SAAS,QAGb;AAED,IAAM,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAE,CAAC;AAExC;;;;;;;;;;;GAWG;AACH,MAAM,CAAC,IAAM,WAAW,GAAG,UACzB,KAAiB,EACjB,aAAoB;IAApB,8BAAA,EAAA,oBAAoB;IAEpB,oDAAoD;IACpD,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;QAAE,OAAO,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;IAEhE,IAAM,SAAS,GAAG,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC;IAEvE,iCAAiC;IACjC,IAAI,GAAG,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAEhC,IAAM,UAAU,GAAa,EAAE,CAAC;IAEhC,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,EAAE;QAC9B,IAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;QAElE,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE;YAC1B,IAAI,KAAK,CAAC,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE;gBAC1B,oEAAoE;gBACpE,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aAC9B;iBAAM;gBACL,IAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;gBACnE,IAAI,cAAc,CAAC,MAAM,CAAC,EAAE;oBAC1B,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;iBAChC;qBAAM;oBACL,sDAAsD;oBACtD,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;iBAC9B;aACF;SACF;aAAM,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;YAChC,kEAAkE;YAClE,iCAAiC;YACjC,GAAG,IAAI,CAAC,CAAC;YACT,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAC9B;aAAM;YACL,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACxB;KACF;IAED,6CAA6C;IAC7C,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM;QAAE,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAErD,OAAO,MAAM,CAAC,aAAa,OAApB,MAAM,EAAkB,UAAU,EAAE;AAC7C,CAAC,CAAC;AAEF;;;;;GAKG;AACH,IAAM,eAAe,GAAG,UAAC,SAAiB;IACxC,OAAA,SAAS,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM;AAA1C,CAA0C,CAAC;AAE7C;;;;;GAKG;AACH,IAAM,cAAc,GAAG,UAAC,SAAiB;IACvC,OAAA,SAAS,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM;AAA1C,CAA0C,CAAC;AAE7C;;;;;;;GAOG;AACH,IAAM,YAAY,GAAG,UAAC,KAAa,EAAE,MAAc,EAAE,SAAoB;IACvE,yEAAyE;IACzE,4EAA4E;IAC5E,kBAAkB;IAClB,IAAI,SAAS,KAAK,SAAS,CAAC,YAAY;QAAE,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;IACvE,IAAI,SAAS,KAAK,SAAS,CAAC,SAAS;QAAE,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;IACpE,MAAM,IAAI,KAAK,CAAC,wBAAsB,SAAW,CAAC,CAAC;AACrD,CAAC,CAAC;AAEF;;;;;;;GAOG;AACH,kBAAkB;AAClB,IAAM,OAAO,GAAG,UAAC,KAAiB,IAAgB,OAAA,CAC9C,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS;IACnD,CAAC,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,YAAY;QACzD,CAAC,CAAC,SAAS,CAAC,SAAS,CACtB,EAJiD,CAIjD,CAAC;AAEF,IAAM,oBAAoB,GAAG,UAAC,KAAiB;IAC7C,OAAA,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI;AAAtC,CAAsC,CAAC;AAEzC,IAAM,uBAAuB,GAAG,UAAC,KAAiB;IAChD,OAAA,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI;AAAtC,CAAsC,CAAC;AAEzC,MAAM,CAAC,IAAM,WAAW,GAAG,UAAC,KAAiB;IAC3C,OAAA,oBAAoB,CAAC,KAAK,CAAC,IAAI,uBAAuB,CAAC,KAAK,CAAC;AAA7D,CAA6D,CAAC"}