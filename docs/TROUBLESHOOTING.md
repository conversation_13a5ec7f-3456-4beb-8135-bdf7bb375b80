# Troubleshooting Guide

This guide helps you resolve common issues when using the Bitcoin Forensic Investigation Tool.

## 🚨 Emergency Help for Scam Victims

If you're a scam victim and need immediate help:

1. **Call Local Police**: Report the crime immediately
2. **FBI IC3**: File online complaint at ic3.gov (US residents)
3. **Document Everything**: Save all communications with the scammer
4. **Use This Tool**: Run the investigation to gather evidence

## 📋 Common Issues and Solutions

### Installation Problems

#### Issue: "npm command not found"
**Solution**: Install Node.js first
```bash
# Download and install Node.js from https://nodejs.org/
# Choose the LTS (Long Term Support) version
# After installation, restart your terminal and try again
npm --version  # Should show version number
```

#### Issue: "Permission denied" during installation
**Solution**: Use proper permissions
```bash
# On macOS/Linux, use sudo:
sudo npm install -g bitcoin-forensic-investigator

# Or configure npm to use a different directory:
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
npm install -g bitcoin-forensic-investigator
```

#### Issue: "Module not found" errors
**Solution**: Clear npm cache and reinstall
```bash
npm cache clean --force
npm uninstall -g bitcoin-forensic-investigator
npm install -g bitcoin-forensic-investigator
```

### Investigation Issues

#### Issue: "Invalid transaction ID" error
**Symptoms**: Tool rejects your transaction ID
**Solution**: 
- Transaction IDs must be exactly 64 characters
- Only contain letters a-f and numbers 0-9
- Example valid TXID: `a1b2c3d4e5f67890123456789012345678901234567890123456789012345678`
- Check your wallet or exchange for the correct transaction ID

#### Issue: "Invalid Bitcoin address" error
**Symptoms**: Tool rejects the Bitcoin address
**Solution**:
- Bitcoin addresses can start with 1, 3, or bc1
- Check for typos - Bitcoin addresses are case-sensitive
- Copy the address directly from your wallet or exchange
- Example valid addresses:
  - Legacy: `**********************************`
  - SegWit: `**********************************`
  - Bech32: `******************************************`

#### Issue: "Transaction not found" error
**Symptoms**: Tool can't find your transaction on the blockchain
**Solution**:
- Wait for transaction confirmation (can take 10-60 minutes)
- Check if the transaction ID is correct
- Verify the transaction exists on a blockchain explorer like blockchain.info
- If transaction is very recent, try again in 30 minutes

#### Issue: "API request failed" errors
**Symptoms**: Tool shows network or API errors
**Solution**:
- Check your internet connection
- Wait a few minutes and try again (may be temporary API issues)
- If problem persists, try using a VPN
- Check if your firewall is blocking the connection

### Performance Issues

#### Issue: Investigation takes too long
**Symptoms**: Tool seems stuck or very slow
**Solution**:
- Reduce the maximum depth (try 3-5 instead of 10)
- Check your internet connection speed
- Close other applications using internet bandwidth
- For complex investigations, let it run overnight

#### Issue: "Out of memory" errors
**Symptoms**: Tool crashes with memory errors
**Solution**:
- Reduce the maximum investigation depth
- Close other applications to free memory
- Restart your computer and try again
- If investigating many transactions, break into smaller chunks

### Report Generation Issues

#### Issue: "Permission denied" when saving reports
**Symptoms**: Can't save reports to disk
**Solution**:
- Check if the output directory exists and is writable
- Try running from a different directory
- On Windows, run command prompt as administrator
- Change output directory: `--output ./my-reports`

#### Issue: Reports are empty or incomplete
**Symptoms**: Generated reports have no data
**Solution**:
- Check if the investigation completed successfully
- Look for error messages in the terminal output
- Try running the investigation again
- Check the logs directory for detailed error information

### Network and Connectivity Issues

#### Issue: "Rate limit exceeded" errors
**Symptoms**: Tool shows rate limiting messages
**Solution**:
- Wait 5-10 minutes before trying again
- The tool automatically handles rate limiting
- If persistent, try again later when API load is lower

#### Issue: "Connection timeout" errors
**Symptoms**: Requests to blockchain APIs timeout
**Solution**:
- Check your internet connection
- Try using a different network (mobile hotspot)
- Increase timeout in configuration
- Try again during off-peak hours

## 🔧 Advanced Troubleshooting

### Enable Debug Logging

To get more detailed error information:

```bash
# Set debug logging level
export LOG_LEVEL=debug
btc-forensics investigate

# Or use verbose flag
btc-forensics investigate --verbose
```

### Check System Requirements

Verify your system meets requirements:

```bash
# Check Node.js version (should be 16+)
node --version

# Check available memory
# On macOS/Linux:
free -h
# On Windows:
systeminfo | findstr "Available Physical Memory"

# Check disk space
df -h  # macOS/Linux
dir   # Windows
```

### Clear Application Data

If you're experiencing persistent issues:

```bash
# Clear npm cache
npm cache clean --force

# Remove and reinstall the tool
npm uninstall -g bitcoin-forensic-investigator
npm install -g bitcoin-forensic-investigator

# Clear any local configuration (if exists)
rm -rf ~/.btc-forensics  # macOS/Linux
rmdir /s ~/.btc-forensics  # Windows
```

## 📞 Getting Additional Help

### Self-Help Resources

1. **Check the logs**: Look in the `logs/` directory for detailed error information
2. **Read the User Guide**: [USER_GUIDE.md](../USER_GUIDE.md) has step-by-step instructions
3. **Review Examples**: [EXAMPLES.md](../EXAMPLES.md) shows common use cases
4. **Check GitHub Issues**: Search for similar problems on our GitHub repository

### Contact Support

If you still need help:

1. **GitHub Issues**: Report bugs and get community help
2. **Documentation**: Check all documentation files in the project
3. **Community Forums**: Join cryptocurrency forensics communities

### For Law Enforcement

Professional support is available for law enforcement agencies:
- Technical training and consultation
- Custom deployment assistance
- Integration with existing forensic workflows

## ⚠️ Important Notes

### Data Safety
- This tool only reads public blockchain data
- No private keys or sensitive information is transmitted
- All processing happens locally on your computer

### Legal Considerations
- Use only for legitimate forensic purposes
- Comply with all applicable laws and regulations
- Consult with legal professionals for court proceedings

### Limitations
- Cannot guarantee fund recovery
- Depends on public blockchain data availability
- Some advanced privacy techniques may limit tracing ability

## 🆘 Emergency Contacts

### For Scam Victims (US)
- **FBI Internet Crime Complaint Center**: https://ic3.gov
- **Federal Trade Commission**: https://reportfraud.ftc.gov
- **Local Police**: Call 911 or your local non-emergency number

### International
- **Europol**: https://www.europol.europa.eu/report-a-crime
- **Action Fraud (UK)**: https://www.actionfraud.police.uk
- **ACCC Scamwatch (Australia)**: https://www.scamwatch.gov.au

Remember: Time is critical in cryptocurrency fraud cases. Report immediately and gather evidence quickly.
